/**
 * WhatsApp Web.js Client for WhatsApp AI Assistant
 * Handles WhatsApp Web integration and communicates with Python via IPC
 */

const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');

class WhatsAppClient {
    constructor() {
        this.client = null;
        this.isReady = false;
        this.sessionPath = path.join(__dirname, '..', 'data', 'whatsapp_session');
        this.logFile = path.join(__dirname, '..', 'logs', 'whatsapp_js.log');
        
        // Ensure directories exist
        this.ensureDirectories();
        
        // Initialize client
        this.initializeClient();
        
        // Setup IPC communication
        this.setupIPC();
    }
    
    ensureDirectories() {
        const dirs = [
            path.dirname(this.sessionPath),
            path.dirname(this.logFile)
        ];
        
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }
    
    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} [${level}] ${message}\n`;
        
        // Write to log file
        fs.appendFileSync(this.logFile, logMessage);
        
        // Also log to console
        console.log(`[${level}] ${message}`);
    }
    
    initializeClient() {
        this.client = new Client({
            authStrategy: new LocalAuth({
                clientId: "whatsapp-ai-assistant",
                dataPath: this.sessionPath
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-default-apps',
                    '--no-default-browser-check',
                    '--disable-hang-monitor',
                    '--disable-prompt-on-repost',
                    '--disable-sync',
                    '--disable-translate',
                    '--disable-ipc-flooding-protection',
                    '--memory-pressure-off',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-features=VizDisplayCompositor,VizServiceDisplay',
                    '--single-process',
                    '--no-crash-upload'
                ],
                executablePath: undefined, // Use default Chrome/Chromium
                timeout: 180000, // Increased timeout to 3 minutes
                ignoreDefaultArgs: ['--disable-extensions'],
                handleSIGINT: false,
                handleSIGTERM: false,
                handleSIGHUP: false,
                ignoreHTTPSErrors: true,
                defaultViewport: null,
                devtools: false
            }
        });

        this.setupEventHandlers();
        this.setupErrorHandlers();
    }
    
    setupEventHandlers() {
        // QR Code generation
        this.client.on('qr', (qr) => {
            this.log('QR Code received, scan with your phone');
            qrcode.generate(qr, { small: true });
            
            // Send QR to Python
            this.sendToPython({
                type: 'qr_code',
                data: qr
            });
        });
        
        // Authentication success
        this.client.on('authenticated', () => {
            this.log('Authentication successful');
            this.sendToPython({
                type: 'authenticated',
                data: true
            });
        });
        
        // Authentication failure
        this.client.on('auth_failure', (msg) => {
            this.log(`Authentication failed: ${msg}`, 'ERROR');
            this.sendToPython({
                type: 'auth_failure',
                data: msg
            });
        });
        
        // Client ready
        this.client.on('ready', () => {
            this.isReady = true;
            this.log('WhatsApp client is ready');
            this.sendToPython({
                type: 'ready',
                data: true
            });
        });
        
        // Incoming messages
        this.client.on('message', async (message) => {
            try {
                const messageData = await this.processMessage(message);
                this.sendToPython({
                    type: 'message',
                    data: messageData
                });
            } catch (error) {
                this.log(`Error processing message: ${error.message}`, 'ERROR');
            }
        });
        
        // Message acknowledgment
        this.client.on('message_ack', (message, ack) => {
            this.sendToPython({
                type: 'message_ack',
                data: {
                    id: message.id._serialized,
                    ack: ack
                }
            });
        });
        
        // Disconnection
        this.client.on('disconnected', (reason) => {
            this.log(`Client disconnected: ${reason}`, 'WARNING');
            this.isReady = false;
            this.sendToPython({
                type: 'disconnected',
                data: reason
            });
        });
        
        // Group join
        this.client.on('group_join', (notification) => {
            this.sendToPython({
                type: 'group_join',
                data: {
                    chatId: notification.chatId,
                    who: notification.who,
                    timestamp: notification.timestamp
                }
            });
        });
        
        // Group leave
        this.client.on('group_leave', (notification) => {
            this.sendToPython({
                type: 'group_leave',
                data: {
                    chatId: notification.chatId,
                    who: notification.who,
                    timestamp: notification.timestamp
                }
            });
        });
    }
    
    async processMessage(message) {
        const contact = await message.getContact();
        const chat = await message.getChat();
        
        // Log the message with higher visibility
        const sender = message.fromMe ? 'You' : (contact.name || contact.pushname || 'Unknown');
        const chatName = chat.name || 'Unknown Chat';
        const messagePreview = message.body.length > 50 ? `${message.body.substring(0, 50)}...` : message.body;
        
        // Add more visible markers to message logs
        if (message.fromMe) {
            this.log(`>>> MESSAGE SENT >>> To: ${chatName} | Content: ${messagePreview}`);
        } else {
            this.log(`<<< MESSAGE RECEIVED <<< From: ${sender} in ${chatName} | Content: ${messagePreview}`);
        }
        
        // Log additional details for debugging
        this.log(`Message details - ID: ${message.id._serialized}, Type: ${message.type}, HasMedia: ${message.hasMedia}, IsForwarded: ${message.isForwarded}`, 'DEBUG');
        
        // Force flush the log file to ensure it's written immediately
        try {
            fs.fsyncSync(fs.openSync(this.logFile, 'a'));
        } catch (error) {
            console.error(`Error flushing log file: ${error.message}`);
        }
        
        return {
            id: message.id._serialized,
            body: message.body,
            type: message.type,
            timestamp: message.timestamp,
            from: message.from,
            to: message.to,
            isForwarded: message.isForwarded,
            isStatus: message.isStatus,
            isStarred: message.isStarred,
            broadcast: message.broadcast,
            fromMe: message.fromMe,
            hasMedia: message.hasMedia,
            hasQuotedMsg: message.hasQuotedMsg,
            location: message.location,
            mentionedIds: message.mentionedIds,
            contact: {
                id: contact.id._serialized,
                name: contact.name,
                pushname: contact.pushname,
                number: contact.number,
                isMyContact: contact.isMyContact,
                isUser: contact.isUser,
                isGroup: contact.isGroup,
                isWAContact: contact.isWAContact,
                profilePicUrl: await contact.getProfilePicUrl().catch(() => null)
            },
            chat: {
                id: chat.id._serialized,
                name: chat.name,
                isGroup: chat.isGroup,
                isReadOnly: chat.isReadOnly,
                unreadCount: chat.unreadCount,
                timestamp: chat.timestamp,
                archived: chat.archived,
                pinned: chat.pinned,
                isMuted: chat.isMuted
            }
        };
    }

    setupErrorHandlers() {
        // Handle client-specific errors
        this.client.on('error', (error) => {
            this.log(`WhatsApp client error: ${error.message}`, 'ERROR');
            this.sendToPython({
                type: 'error',
                data: {
                    type: 'client_error',
                    error: error.message
                }
            });
        });

        // Handle browser disconnection
        this.client.on('browser_disconnected', () => {
            this.log('Browser disconnected unexpectedly', 'ERROR');
            this.sendToPython({
                type: 'error',
                data: {
                    type: 'browser_disconnected',
                    error: 'Browser process was terminated'
                }
            });
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            this.log(`Uncaught exception: ${error.message}`, 'ERROR');
            this.log(`Stack trace: ${error.stack}`, 'ERROR');
            this.sendToPython({
                type: 'error',
                data: {
                    type: 'uncaught_exception',
                    error: error.message,
                    stack: error.stack
                }
            });

            // Attempt graceful shutdown
            this.shutdown();
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            this.log(`Unhandled rejection: ${reason}`, 'ERROR');
            this.log(`Promise: ${promise}`, 'ERROR');
            this.sendToPython({
                type: 'error',
                data: {
                    type: 'unhandled_rejection',
                    error: reason.toString()
                }
            });
        });
    }

    setupIPC() {
        // Listen for commands from Python via stdin
        process.stdin.setEncoding('utf8');
        process.stdin.on('data', (data) => {
            try {
                const commands = data.trim().split('\n');
                commands.forEach(commandStr => {
                    if (commandStr.trim()) {
                        const command = JSON.parse(commandStr);
                        this.handleCommand(command);
                    }
                });
            } catch (error) {
                this.log(`Error parsing command: ${error.message}`, 'ERROR');
            }
        });
        
        // Handle process termination
        process.on('SIGINT', () => {
            this.log('Received SIGINT, shutting down gracefully');
            this.shutdown();
        });
        
        process.on('SIGTERM', () => {
            this.log('Received SIGTERM, shutting down gracefully');
            this.shutdown();
        });
    }
    
    async handleCommand(command) {
        try {
            switch (command.type) {
                case 'send_message':
                    await this.sendMessage(command.data);
                    break;
                case 'get_chats':
                    await this.getChats();
                    break;
                case 'get_contacts':
                    await this.getContacts();
                    break;
                case 'get_chat_by_id':
                    await this.getChatById(command.data.chatId);
                    break;
                case 'mark_chat_unread':
                    await this.markChatUnread(command.data.chatId);
                    break;
                case 'archive_chat':
                    await this.archiveChat(command.data.chatId);
                    break;
                case 'pin_chat':
                    await this.pinChat(command.data.chatId);
                    break;
                case 'mute_chat':
                    await this.muteChat(command.data.chatId, command.data.duration);
                    break;
                case 'get_profile_pic':
                    await this.getProfilePic(command.data.contactId);
                    break;
                case 'set_status':
                    await this.setStatus(command.data.status);
                    break;
                case 'logout':
                    await this.logout();
                    break;
                case 'restart':
                    await this.restart();
                    break;
                default:
                    this.log(`Unknown command type: ${command.type}`, 'WARNING');
            }
        } catch (error) {
            this.log(`Error handling command ${command.type}: ${error.message}`, 'ERROR');
            this.sendToPython({
                type: 'error',
                data: {
                    command: command.type,
                    error: error.message
                }
            });
        }
    }
    
    async sendMessage(data) {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }
        
        const { chatId, message, options = {} } = data;
        
        try {
            const sentMessage = await this.client.sendMessage(chatId, message, options);
            
            this.sendToPython({
                type: 'message_sent',
                data: {
                    id: sentMessage.id._serialized,
                    chatId: chatId,
                    message: message,
                    timestamp: sentMessage.timestamp
                }
            });
        } catch (error) {
            throw new Error(`Failed to send message: ${error.message}`);
        }
    }
    
    async getChats() {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }
        
        const chats = await this.client.getChats();
        const chatData = chats.map(chat => ({
            id: chat.id._serialized,
            name: chat.name,
            isGroup: chat.isGroup,
            isReadOnly: chat.isReadOnly,
            unreadCount: chat.unreadCount,
            timestamp: chat.timestamp,
            archived: chat.archived,
            pinned: chat.pinned,
            isMuted: chat.isMuted
        }));
        
        this.sendToPython({
            type: 'chats_list',
            data: chatData
        });
    }
    
    async getContacts() {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }
        
        const contacts = await this.client.getContacts();
        const contactData = contacts.map(contact => ({
            id: contact.id._serialized,
            name: contact.name,
            pushname: contact.pushname,
            number: contact.number,
            isMyContact: contact.isMyContact,
            isUser: contact.isUser,
            isGroup: contact.isGroup,
            isWAContact: contact.isWAContact
        }));
        
        this.sendToPython({
            type: 'contacts_list',
            data: contactData
        });
    }
    
    sendToPython(data) {
        try {
            // Ensure all strings are properly encoded
            const sanitizedData = this.sanitizeDataForJSON(data);
            const message = JSON.stringify(sanitizedData) + '\n';
            process.stdout.write(message);
        } catch (error) {
            this.log(`Error sending data to Python: ${error.message}`, 'ERROR');
            // Send a simplified error message that can be safely encoded
            process.stdout.write(JSON.stringify({
                type: 'error',
                data: {
                    message: `Failed to send data: ${error.message}`,
                    originalType: data.type
                }
            }) + '\n');
        }
    }
    
    sanitizeDataForJSON(data) {
        // Deep clone and sanitize the data to ensure it can be safely JSON-encoded
        if (data === null || data === undefined) {
            return data;
        }
        
        if (typeof data !== 'object') {
            // For primitive types, just return them
            return data;
        }
        
        if (Array.isArray(data)) {
            // For arrays, sanitize each element
            return data.map(item => this.sanitizeDataForJSON(item));
        }
        
        // For objects, sanitize each property
        const sanitized = {};
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string') {
                // Replace any problematic characters in strings
                sanitized[key] = value.replace(/[\uD800-\uDFFF]/g, ''); // Remove surrogate pairs
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeDataForJSON(value);
            } else {
                sanitized[key] = value;
            }
        }
        
        return sanitized;
    }
    
    async start() {
        this.log('Starting WhatsApp client...');
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                // Clean up any existing session if this is a retry
                if (retryCount > 0) {
                    this.log(`Retry attempt ${retryCount}/${maxRetries}`, 'INFO');
                    await this.cleanupSession();
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
                }

                await this.client.initialize();
                this.log('WhatsApp client initialized successfully', 'INFO');
                return;

            } catch (error) {
                retryCount++;
                this.log(`Failed to start client (attempt ${retryCount}/${maxRetries}): ${error.message}`, 'ERROR');

                // Send error details to Python
                this.sendToPython({
                    type: 'error',
                    data: {
                        type: 'initialization_error',
                        error: error.message,
                        attempt: retryCount,
                        maxRetries: maxRetries
                    }
                });

                if (retryCount >= maxRetries) {
                    this.log('Max retries exceeded, exiting...', 'ERROR');
                    process.exit(1);
                }

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 10000));
            }
        }
    }

    async cleanupSession() {
        this.log('Cleaning up session files...', 'INFO');
        try {
            if (fs.existsSync(this.sessionPath)) {
                const files = fs.readdirSync(this.sessionPath);
                for (const file of files) {
                    const filePath = path.join(this.sessionPath, file);
                    try {
                        if (fs.statSync(filePath).isDirectory()) {
                            fs.rmSync(filePath, { recursive: true, force: true });
                        } else {
                            fs.unlinkSync(filePath);
                        }
                        this.log(`Removed session file: ${file}`, 'DEBUG');
                    } catch (err) {
                        this.log(`Could not remove session file ${file}: ${err.message}`, 'WARNING');
                    }
                }
            }
        } catch (error) {
            this.log(`Error cleaning up session: ${error.message}`, 'WARNING');
        }
    }
    
    async logout() {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }

        this.log('Logging out from WhatsApp...');
        await this.client.logout();
    }

    async restart() {
        this.log('Restarting WhatsApp client...');
        await this.shutdown();
        // The process will be restarted by the Python manager
    }

    async shutdown() {
        this.log('Shutting down WhatsApp client...');
        try {
            if (this.client) {
                this.log('Destroying WhatsApp client...', 'INFO');
                await this.client.destroy();
                this.log('WhatsApp client destroyed', 'INFO');
            }

            // Give some time for cleanup
            await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
            this.log(`Error during shutdown: ${error.message}`, 'ERROR');
        } finally {
            this.log('WhatsApp client shutdown complete', 'INFO');
            process.exit(0);
        }
    }
}

// Start the client
const whatsappClient = new WhatsAppClient();
whatsappClient.start();
