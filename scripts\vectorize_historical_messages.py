#!/usr/bin/env python3
"""
Historical Message Vectorization Script for WhatsApp AI Assistant.
Processes and vectorizes all existing messages in the database to improve AI context.
"""

import sys
import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from sqlalchemy import text
from data.database import get_database_manager
from data.models import Message, Contact
from core.ai_engine import AIEngine
from utils.logging import get_logger

logger = get_logger(__name__)


class HistoricalMessageVectorizer:
    """Handles vectorization of historical messages."""
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.ai_engine = None
        self.processed_count = 0
        self.error_count = 0

    async def initialize(self):
        """Initialize AI engine."""
        try:
            logger.info("Initializing AI engine...")
            self.ai_engine = AIEngine()
            await self.ai_engine.initialize()

            logger.info("Initialization completed successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            return False
    
    def get_unprocessed_messages(self, batch_size: int = 50) -> List[Dict[str, Any]]:
        """Get batch of unprocessed messages from database."""
        try:
            with self.db_manager.get_session() as session:
                # Get messages that haven't been processed yet
                result = session.execute(text("""
                    SELECT m.id, m.chat_id, m.contact_id, m.message_text, 
                           m.sender_name, m.timestamp, m.is_from_me,
                           c.name as contact_name, c.phone_number
                    FROM messages m
                    LEFT JOIN contacts c ON m.contact_id = c.id
                    WHERE m.processed = 0 
                    AND m.message_text IS NOT NULL 
                    AND m.message_text != ''
                    ORDER BY m.timestamp ASC
                    LIMIT :batch_size
                """), {"batch_size": batch_size})
                
                messages = []
                for row in result:
                    messages.append({
                        'id': row[0],
                        'chat_id': row[1],
                        'contact_id': row[2],
                        'message_text': row[3],
                        'sender_name': row[4],
                        'timestamp': row[5],
                        'is_from_me': row[6],
                        'contact_name': row[7],
                        'phone_number': row[8]
                    })
                
                return messages
                
        except Exception as e:
            logger.error(f"Error getting unprocessed messages: {e}")
            return []
    
    async def process_message_batch(self, messages: List[Dict[str, Any]]) -> int:
        """Process a batch of messages."""
        processed_in_batch = 0
        
        for message in messages:
            try:
                # Process message with AI engine
                await self.process_single_message(message)
                processed_in_batch += 1
                self.processed_count += 1
                
                if self.processed_count % 10 == 0:
                    logger.info(f"Processed {self.processed_count} messages...")
                    
            except Exception as e:
                logger.error(f"Error processing message {message['id']}: {e}")
                self.error_count += 1
                
        return processed_in_batch
    
    async def process_single_message(self, message: Dict[str, Any]):
        """Process a single message."""
        try:
            message_id = message['id']
            message_text = message['message_text']

            # Skip if message is too short or empty
            if not message_text or len(message_text.strip()) < 3:
                await self.mark_message_processed(message_id)
                return

            # Store message embedding using AI engine's method
            if self.ai_engine and hasattr(self.ai_engine, '_store_message_embedding'):
                success = await self.ai_engine._store_message_embedding(
                    message_text=message_text,
                    contact_id=message['contact_id'],
                    chat_id=message['chat_id'],
                    message_id=message_id
                )

                if success:
                    logger.debug(f"Successfully vectorized message {message_id}")
                else:
                    logger.warning(f"Failed to vectorize message {message_id}")

            # Update conversation memory if this is an incoming message
            if not message['is_from_me'] and self.ai_engine:
                try:
                    # Get recent messages for this contact to update memory
                    await self.ai_engine._update_conversation_memory(
                        contact_id=message['contact_id'],
                        message_text=message_text,
                        response_result={'processed_historically': True}
                    )
                except Exception as e:
                    logger.debug(f"Could not update conversation memory for message {message_id}: {e}")

            # Mark as processed
            await self.mark_message_processed(message_id)

        except Exception as e:
            logger.error(f"Error in process_single_message: {e}")
            raise
    
    async def mark_message_processed(self, message_id: int):
        """Mark message as processed in database."""
        try:
            with self.db_manager.get_session() as session:
                session.execute(text("""
                    UPDATE messages
                    SET processed = 1
                    WHERE id = :message_id
                """), {"message_id": message_id})
                session.commit()

        except Exception as e:
            logger.error(f"Error marking message {message_id} as processed: {e}")
    
    def get_processing_stats(self) -> Dict[str, int]:
        """Get current processing statistics."""
        try:
            with self.db_manager.get_session() as session:
                # Total messages
                result = session.execute(text("SELECT COUNT(*) FROM messages"))
                total_messages = result.scalar()
                
                # Processed messages
                result = session.execute(text("SELECT COUNT(*) FROM messages WHERE processed = 1"))
                processed_messages = result.scalar()
                
                # Unprocessed messages
                unprocessed_messages = total_messages - processed_messages
                
                return {
                    'total_messages': total_messages,
                    'processed_messages': processed_messages,
                    'unprocessed_messages': unprocessed_messages,
                    'session_processed': self.processed_count,
                    'session_errors': self.error_count
                }
                
        except Exception as e:
            logger.error(f"Error getting processing stats: {e}")
            return {}
    
    async def vectorize_all_messages(self, batch_size: int = 50):
        """Vectorize all unprocessed messages."""
        logger.info("Starting historical message vectorization...")
        
        # Get initial stats
        stats = self.get_processing_stats()
        logger.info(f"Initial stats: {stats}")
        
        if stats.get('unprocessed_messages', 0) == 0:
            logger.info("No unprocessed messages found")
            return True
        
        try:
            while True:
                # Get batch of unprocessed messages
                messages = self.get_unprocessed_messages(batch_size)
                
                if not messages:
                    logger.info("No more messages to process")
                    break
                
                logger.info(f"Processing batch of {len(messages)} messages...")
                
                # Process the batch
                processed_count = await self.process_message_batch(messages)
                
                if processed_count == 0:
                    logger.warning("No messages processed in this batch, stopping")
                    break
                
                # Small delay between batches to avoid overwhelming the system
                await asyncio.sleep(1)
            
            # Final stats
            final_stats = self.get_processing_stats()
            logger.info(f"Vectorization completed. Final stats: {final_stats}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during vectorization: {e}")
            return False


async def main():
    """Main function."""
    print("WhatsApp AI Assistant - Historical Message Vectorization")
    print("=" * 60)
    
    vectorizer = HistoricalMessageVectorizer()
    
    # Initialize components
    print("Initializing components...")
    if not await vectorizer.initialize():
        print("❌ Failed to initialize components")
        return False
    
    print("✅ Components initialized successfully")
    
    # Get initial stats
    stats = vectorizer.get_processing_stats()
    print(f"📊 Messages to process: {stats.get('unprocessed_messages', 0)}")
    
    if stats.get('unprocessed_messages', 0) == 0:
        print("✅ All messages are already processed")
        return True
    
    # Start vectorization
    print("🚀 Starting vectorization process...")
    success = await vectorizer.vectorize_all_messages()
    
    if success:
        print("✅ Vectorization completed successfully")
        final_stats = vectorizer.get_processing_stats()
        print(f"📊 Final stats: {final_stats}")
        return True
    else:
        print("❌ Vectorization failed")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
