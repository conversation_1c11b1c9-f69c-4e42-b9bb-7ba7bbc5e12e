"""
Main entry point for WhatsApp AI Assistant.
Initializes all components and starts the application.
"""

import asyncio
import signal
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.config import get_config
from utils.logging import setup_logging, get_app_logger
from data.database import init_database
from integrations.whatsapp_manager import WhatsAppManager
from integrations.telegram_manager import TelegramManager
from core.ai_engine import AIEngine


class WhatsAppAssistant:
    """Main application class."""

    def __init__(self):
        self.config = get_config()
        self.logger = get_app_logger()
        self.running = False

        # Components will be initialized later
        self.db_manager = None
        self.whatsapp_manager = None
        self.telegram_manager = None
        self.ai_engine = None
        self.scheduler = None

    async def initialize(self):
        """Initialize all application components."""
        self.logger.info("Initializing WhatsApp AI Assistant...")

        try:
            # Initialize database
            self.logger.info("Initializing database...")
            self.db_manager = init_database()

            if not self.db_manager.health_check():
                raise Exception("Database health check failed")

            self.logger.info("Database initialized successfully")

            # Initialize WhatsApp manager
            self.logger.info("Initializing WhatsApp manager...")
            self.whatsapp_manager = WhatsAppManager()

            # Initialize AI engine
            self.logger.info("Initializing AI engine...")
            self.ai_engine = AIEngine()

            if await self.ai_engine.initialize():
                self.logger.info("AI engine initialized successfully")
            else:
                raise Exception("Failed to initialize AI engine")

            # Initialize WhatsApp manager with AI engine
            if await self.whatsapp_manager.initialize(self.ai_engine):
                self.logger.info("WhatsApp manager initialized successfully")
            else:
                raise Exception("Failed to initialize WhatsApp manager")

            # Initialize Telegram manager
            self.logger.info("Initializing Telegram manager...")
            self.telegram_manager = TelegramManager(self.whatsapp_manager)

            if await self.telegram_manager.initialize():
                self.logger.info("Telegram manager initialized successfully")
            else:
                raise Exception("Failed to initialize Telegram manager")

            # TODO: Initialize other components
            # self.scheduler = TaskScheduler()

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            raise

    async def start(self):
        """Start the application."""
        self.logger.info("Starting WhatsApp AI Assistant...")

        try:
            await self.initialize()

            # Start WhatsApp integration
            if self.whatsapp_manager:
                self.logger.info("Starting WhatsApp integration...")
                if await self.whatsapp_manager.start():
                    self.logger.info("WhatsApp integration started successfully")
                else:
                    self.logger.error("Failed to start WhatsApp integration")

            # Start Telegram integration
            if self.telegram_manager:
                self.logger.info("Starting Telegram integration...")
                if await self.telegram_manager.start():
                    self.logger.info("Telegram integration started successfully")
                else:
                    self.logger.error("Failed to start Telegram integration")

            # Start AI engine
            if self.ai_engine:
                self.logger.info("Starting AI engine...")
                if await self.ai_engine.start():
                    self.logger.info("AI engine started successfully")
                else:
                    self.logger.error("Failed to start AI engine")

            self.running = True
            self.logger.info("WhatsApp AI Assistant started successfully")

            # Main application loop
            while self.running:
                await asyncio.sleep(1)
                # TODO: Add main application logic here

        except KeyboardInterrupt:
            self.logger.info("Received shutdown signal")
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            raise
        finally:
            await self.shutdown()

    async def shutdown(self):
        """Shutdown the application gracefully."""
        self.logger.info("Shutting down WhatsApp AI Assistant...")

        self.running = False

        # Shutdown components
        if self.ai_engine:
            await self.ai_engine.stop()

        if self.telegram_manager:
            await self.telegram_manager.stop()

        if self.whatsapp_manager:
            await self.whatsapp_manager.stop()

        # TODO: Shutdown other components
        # if self.scheduler:
        #     await self.scheduler.shutdown()

        self.logger.info("WhatsApp AI Assistant shutdown complete")

    def handle_signal(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}")
        self.running = False


async def main():
    """Main function."""
    # Setup logging
    setup_logging()

    # Create and start application
    app = WhatsAppAssistant()

    # Setup signal handlers
    signal.signal(signal.SIGINT, app.handle_signal)
    signal.signal(signal.SIGTERM, app.handle_signal)

    try:
        await app.start()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("Python 3.8 or higher is required")
        sys.exit(1)

    # Run the application
    asyncio.run(main())
