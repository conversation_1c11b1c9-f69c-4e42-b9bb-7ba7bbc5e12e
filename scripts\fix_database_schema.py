#!/usr/bin/env python3
"""
Database schema fix script for WhatsApp AI Assistant.
Fixes FTS triggers and schema issues that cause "no such column: T.contact_id" errors.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from sqlalchemy import text
from data.database import get_database_manager
from utils.logging import get_database_logger

logger = get_database_logger()


def fix_fts_triggers():
    """Fix FTS triggers that might be causing schema issues."""
    db_manager = get_database_manager()
    
    try:
        with db_manager.engine.connect() as conn:
            # Start transaction
            trans = conn.begin()
            
            try:
                logger.info("Dropping existing FTS triggers...")
                
                # Drop existing triggers
                conn.execute(text("DROP TRIGGER IF EXISTS contacts_fts_insert"))
                conn.execute(text("DROP TRIGGER IF EXISTS contacts_fts_update"))
                conn.execute(text("DROP TRIGGER IF EXISTS contacts_fts_delete"))
                conn.execute(text("DROP TRIGGER IF EXISTS messages_fts_insert"))
                conn.execute(text("DROP TRIGGER IF EXISTS messages_fts_update"))
                conn.execute(text("DROP TRIGGER IF EXISTS messages_fts_delete"))
                
                logger.info("Dropping existing FTS tables...")
                
                # Drop FTS tables
                conn.execute(text("DROP TABLE IF EXISTS contacts_fts"))
                conn.execute(text("DROP TABLE IF EXISTS messages_fts"))
                
                logger.info("Recreating FTS tables...")
                
                # Recreate FTS tables
                conn.execute(
                    text(
                        """
                    CREATE VIRTUAL TABLE IF NOT EXISTS contacts_fts USING fts5(
                        contact_id UNINDEXED,
                        name,
                        nickname,
                        notes,
                        content='contacts',
                        content_rowid='id'
                    )
                """
                    )
                )
                
                conn.execute(
                    text(
                        """
                    CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
                        message_id UNINDEXED,
                        message_text,
                        sender_name,
                        content='messages',
                        content_rowid='id'
                    )
                """
                    )
                )
                
                logger.info("Recreating FTS triggers...")
                
                # Recreate contacts FTS triggers with proper NULL handling
                conn.execute(
                    text(
                        """
                    CREATE TRIGGER IF NOT EXISTS contacts_fts_insert AFTER INSERT ON contacts BEGIN
                        INSERT INTO contacts_fts(contact_id, name, nickname, notes)
                        VALUES (new.id, COALESCE(new.name, ''), COALESCE(new.nickname, ''), COALESCE(new.notes, ''));
                    END
                """
                    )
                )
                
                conn.execute(
                    text(
                        """
                    CREATE TRIGGER IF NOT EXISTS contacts_fts_update AFTER UPDATE ON contacts BEGIN
                        UPDATE contacts_fts SET 
                            name = COALESCE(new.name, ''),
                            nickname = COALESCE(new.nickname, ''),
                            notes = COALESCE(new.notes, '')
                        WHERE contact_id = new.id;
                    END
                """
                    )
                )
                
                conn.execute(
                    text(
                        """
                    CREATE TRIGGER IF NOT EXISTS contacts_fts_delete AFTER DELETE ON contacts BEGIN
                        DELETE FROM contacts_fts WHERE contact_id = old.id;
                    END
                """
                    )
                )
                
                # Recreate messages FTS triggers
                conn.execute(
                    text(
                        """
                    CREATE TRIGGER IF NOT EXISTS messages_fts_insert AFTER INSERT ON messages BEGIN
                        INSERT INTO messages_fts(message_id, message_text, sender_name)
                        VALUES (new.id, COALESCE(new.message_text, ''), COALESCE(new.sender_name, ''));
                    END
                """
                    )
                )
                
                conn.execute(
                    text(
                        """
                    CREATE TRIGGER IF NOT EXISTS messages_fts_update AFTER UPDATE ON messages BEGIN
                        UPDATE messages_fts SET 
                            message_text = COALESCE(new.message_text, ''),
                            sender_name = COALESCE(new.sender_name, '')
                        WHERE message_id = new.id;
                    END
                """
                    )
                )
                
                conn.execute(
                    text(
                        """
                    CREATE TRIGGER IF NOT EXISTS messages_fts_delete AFTER DELETE ON messages BEGIN
                        DELETE FROM messages_fts WHERE message_id = old.id;
                    END
                """
                    )
                )
                
                logger.info("Populating FTS tables with existing data...")
                
                # Populate FTS tables with existing data
                conn.execute(
                    text(
                        """
                    INSERT INTO contacts_fts(contact_id, name, nickname, notes)
                    SELECT id, COALESCE(name, ''), COALESCE(nickname, ''), COALESCE(notes, '')
                    FROM contacts
                """
                    )
                )
                
                conn.execute(
                    text(
                        """
                    INSERT INTO messages_fts(message_id, message_text, sender_name)
                    SELECT id, COALESCE(message_text, ''), COALESCE(sender_name, '')
                    FROM messages
                """
                    )
                )
                
                # Commit transaction
                trans.commit()
                logger.info("FTS schema fix completed successfully")
                return True
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Error fixing FTS schema: {e}")
                return False
                
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return False


def verify_schema():
    """Verify that the schema is working correctly."""
    db_manager = get_database_manager()
    
    try:
        with db_manager.engine.connect() as conn:
            # Test basic table access
            result = conn.execute(text("SELECT COUNT(*) FROM contacts"))
            contacts_count = result.scalar()
            logger.info(f"Contacts table has {contacts_count} records")
            
            # Test FTS tables
            result = conn.execute(text("SELECT COUNT(*) FROM contacts_fts"))
            fts_count = result.scalar()
            logger.info(f"Contacts FTS table has {fts_count} records")
            
            # Test a simple update to see if triggers work
            conn.execute(text("BEGIN"))
            conn.execute(text("UPDATE contacts SET updated_at = datetime('now') WHERE id = 1"))
            conn.execute(text("ROLLBACK"))
            
            logger.info("Schema verification completed successfully")
            return True
            
    except Exception as e:
        logger.error(f"Schema verification failed: {e}")
        return False


def main():
    """Main function to fix database schema issues."""
    print("WhatsApp AI Assistant - Database Schema Fix")
    print("=" * 50)
    
    try:
        # Fix FTS triggers
        print("Fixing FTS triggers and schema...")
        if fix_fts_triggers():
            print("✅ FTS schema fixed successfully")
        else:
            print("❌ Failed to fix FTS schema")
            return False
        
        # Verify schema
        print("Verifying schema...")
        if verify_schema():
            print("✅ Schema verification passed")
        else:
            print("❌ Schema verification failed")
            return False
        
        print("\n🎉 Database schema fix completed successfully!")
        print("You can now restart the WhatsApp client.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
