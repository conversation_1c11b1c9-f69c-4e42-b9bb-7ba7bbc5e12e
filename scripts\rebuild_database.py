#!/usr/bin/env python3
"""
Database rebuild script for WhatsApp AI Assistant.
Completely rebuilds the database to fix persistent schema issues.
"""

import sys
import os
import shutil
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from data.database import get_database_manager
from utils.logging import get_database_logger

logger = get_database_logger()


def backup_database():
    """Create a backup of the current database."""
    try:
        db_path = Path("data/whatsapp_assistant.db")
        if db_path.exists():
            backup_path = Path("data/whatsapp_assistant_backup.db")
            shutil.copy2(db_path, backup_path)
            logger.info(f"Database backed up to {backup_path}")
            return True
    except Exception as e:
        logger.error(f"Failed to backup database: {e}")
        return False


def rebuild_database():
    """Completely rebuild the database."""
    try:
        # Remove existing database
        db_path = Path("data/whatsapp_assistant.db")
        if db_path.exists():
            db_path.unlink()
            logger.info("Removed existing database")
        
        # Remove any journal files
        for journal_file in db_path.parent.glob("whatsapp_assistant.db*"):
            if journal_file != db_path:
                journal_file.unlink()
                logger.info(f"Removed journal file: {journal_file}")
        
        # Initialize new database
        db_manager = get_database_manager()
        
        # Force initialization
        with db_manager.engine.connect() as conn:
            # Test basic functionality
            result = conn.execute("SELECT 1")
            logger.info("Database connection test successful")
        
        logger.info("Database rebuilt successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to rebuild database: {e}")
        return False


def verify_new_database():
    """Verify the new database is working correctly."""
    try:
        db_manager = get_database_manager()
        
        with db_manager.engine.connect() as conn:
            # Test basic table access
            result = conn.execute("SELECT COUNT(*) FROM contacts")
            contacts_count = result.scalar()
            logger.info(f"Contacts table accessible, count: {contacts_count}")
            
            # Test FTS tables
            try:
                result = conn.execute("SELECT COUNT(*) FROM contacts_fts")
                fts_count = result.scalar()
                logger.info(f"Contacts FTS table accessible, count: {fts_count}")
            except Exception as e:
                logger.warning(f"FTS table issue (expected for new DB): {e}")
            
            # Test a simple insert and update to verify triggers work
            conn.execute("BEGIN")
            try:
                # Insert test contact
                result = conn.execute("""
                    INSERT INTO contacts (phone_number, name, category, consent_given, privacy_level)
                    VALUES ('1234567890', 'Test Contact', 'test', 0, 'private')
                """)
                
                # Get the inserted ID
                contact_id = result.lastrowid
                
                # Update the contact to test triggers
                conn.execute("""
                    UPDATE contacts SET name = 'Updated Test Contact', updated_at = datetime('now')
                    WHERE id = ?
                """, (contact_id,))
                
                # Clean up test data
                conn.execute("DELETE FROM contacts WHERE id = ?", (contact_id,))
                
                conn.execute("COMMIT")
                logger.info("Database operations test successful")
                
            except Exception as e:
                conn.execute("ROLLBACK")
                raise e
            
        return True
        
    except Exception as e:
        logger.error(f"Database verification failed: {e}")
        return False


def main():
    """Main function to rebuild database."""
    print("WhatsApp AI Assistant - Database Rebuild")
    print("=" * 50)
    
    try:
        # Backup existing database
        print("Creating database backup...")
        if backup_database():
            print("✅ Database backup created")
        else:
            print("⚠️  Could not create backup (database may not exist)")
        
        # Rebuild database
        print("Rebuilding database...")
        if rebuild_database():
            print("✅ Database rebuilt successfully")
        else:
            print("❌ Failed to rebuild database")
            return False
        
        # Verify new database
        print("Verifying new database...")
        if verify_new_database():
            print("✅ Database verification passed")
        else:
            print("❌ Database verification failed")
            return False
        
        print("\n🎉 Database rebuild completed successfully!")
        print("The WhatsApp client should now start without schema errors.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
