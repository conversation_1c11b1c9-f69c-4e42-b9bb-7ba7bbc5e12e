#!/usr/bin/env python3
"""
Complete system test for the WhatsApp AI Assistant.
Tests all major components and their integration.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from integrations.telegram_conversation_manager import TelegramConversationManager
from core.ai_engine import AIEngine
from data.database import DatabaseManager
from utils.logging import get_logger

logger = get_logger("test_complete")


async def test_database_connection():
    """Test database connectivity and basic operations."""
    print("🗄️  Testing database connection...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Test basic queries
        async with db.get_session() as session:
            # Test message count
            result = await session.execute("SELECT COUNT(*) as count FROM messages")
            message_count = result.fetchone()['count']
            print(f"   ✅ Database connected - {message_count:,} messages found")
            
            # Test contact count
            result = await session.execute("SELECT COUNT(*) as count FROM contacts")
            contact_count = result.fetchone()['count']
            print(f"   ✅ Contacts table - {contact_count:,} contacts found")
            
            # Test vector count
            result = await session.execute("SELECT COUNT(*) as count FROM message_vectors")
            vector_count = result.fetchone()['count']
            print(f"   ✅ Vector database - {vector_count:,} vectors found")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        return False


async def test_ai_engine():
    """Test AI engine functionality."""
    print("\n🤖 Testing AI engine...")
    
    try:
        ai_engine = AIEngine()
        await ai_engine.initialize()
        
        # Test basic response generation
        test_message = "Hello, can you help me with my WhatsApp messages?"
        response = await ai_engine.generate_response(test_message)
        
        if response and len(response) > 10:
            print(f"   ✅ AI engine responding - {len(response)} characters")
            print(f"   📝 Sample response: {response[:100]}...")
            return True
        else:
            print("   ❌ AI engine not generating proper responses")
            return False
            
    except Exception as e:
        print(f"   ❌ AI engine test failed: {e}")
        return False


async def test_conversation_manager():
    """Test conversation manager with AI integration."""
    print("\n💬 Testing conversation manager...")
    
    try:
        # Initialize with AI engine
        ai_engine = AIEngine()
        await ai_engine.initialize()
        
        conversation_manager = TelegramConversationManager(ai_engine=ai_engine)
        await conversation_manager.initialize()
        
        test_user_id = 999999
        
        # Test 1: Basic conversation
        print("   Test 1: Basic conversation")
        response1 = await conversation_manager.process_message(test_user_id, "Hello!")
        print(f"   Response: {response1[:80]}...")
        
        # Test 2: WhatsApp data query
        print("   Test 2: WhatsApp data query")
        response2 = await conversation_manager.process_message(test_user_id, "How many messages do I have?")
        print(f"   Response: {response2[:80]}...")
        
        # Test 3: Contact search
        print("   Test 3: Contact search")
        response3 = await conversation_manager.process_message(test_user_id, "Who are my most active contacts?")
        print(f"   Response: {response3[:80]}...")
        
        # Test 4: Conversation memory
        print("   Test 4: Conversation memory")
        response4 = await conversation_manager.process_message(test_user_id, "What did I ask you before?")
        print(f"   Response: {response4[:80]}...")
        
        # Check conversation stats
        stats = conversation_manager.get_conversation_stats()
        print(f"   📊 Conversation stats: {stats['active_conversations']} active, {stats['total_memory_messages']} in memory")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Conversation manager test failed: {e}")
        return False


async def test_whatsapp_data_integration():
    """Test WhatsApp data access and search functionality."""
    print("\n📱 Testing WhatsApp data integration...")
    
    try:
        conversation_manager = TelegramConversationManager()
        await conversation_manager.initialize()
        
        # Test recent messages
        recent_messages = await conversation_manager._get_recent_messages(10)
        print(f"   ✅ Recent messages: {len(recent_messages)} found")
        
        # Test active conversations
        active_conversations = await conversation_manager._get_active_conversations()
        print(f"   ✅ Active conversations: {len(active_conversations)} found")
        
        # Test message statistics
        stats = await conversation_manager._get_message_statistics()
        print(f"   ✅ Message statistics: {stats.get('total_messages', 0):,} total, {stats.get('unique_contacts', 0)} contacts")
        
        # Test contact search
        if recent_messages:
            sample_text = recent_messages[0]['message_text']
            mentioned_contacts = await conversation_manager._find_mentioned_contacts(sample_text)
            print(f"   ✅ Contact search: {len(mentioned_contacts)} contacts found in sample text")
        
        return True
        
    except Exception as e:
        print(f"   ❌ WhatsApp data integration test failed: {e}")
        return False


async def test_vector_search():
    """Test vector search functionality."""
    print("\n🔍 Testing vector search...")
    
    try:
        conversation_manager = TelegramConversationManager()
        await conversation_manager.initialize()
        
        # Test vector search
        search_query = "meeting tomorrow"
        results = await conversation_manager._search_relevant_messages(search_query, limit=5)
        
        if results:
            print(f"   ✅ Vector search: {len(results)} relevant messages found for '{search_query}'")
            for i, result in enumerate(results[:2]):
                print(f"   📄 Result {i+1}: {result['message_text'][:60]}...")
        else:
            print(f"   ⚠️  Vector search: No results found for '{search_query}'")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Vector search test failed: {e}")
        return False


async def main():
    """Run complete system test."""
    print("🚀 Starting Complete System Test\n")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Database Connection", test_database_connection),
        ("AI Engine", test_ai_engine),
        ("Conversation Manager", test_conversation_manager),
        ("WhatsApp Data Integration", test_whatsapp_data_integration),
        ("Vector Search", test_vector_search),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"   💥 {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:10} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The WhatsApp AI Assistant is fully functional.")
        print("\n🔧 System Features:")
        print("   ✅ Database connectivity and data access")
        print("   ✅ AI engine with OpenAI integration")
        print("   ✅ Conversation management with memory")
        print("   ✅ WhatsApp message analysis and search")
        print("   ✅ Vector-based semantic search")
        print("   ✅ Telegram bot integration ready")
        
        print("\n📱 Ready for use! You can now:")
        print("   • Send messages to the Telegram bot")
        print("   • Ask questions about your WhatsApp data")
        print("   • Search for specific messages or conversations")
        print("   • Get statistics and insights about your messaging")
        
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check the logs for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
