"""
WhatsApp Manager for WhatsApp AI Assistant.
Main integration manager that coordinates all WhatsApp-related components.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from utils.logging import get_whatsapp_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import SystemLog

from .whatsapp_client import WhatsAppClient
from .message_listener import MessageListener
from .message_sender import MessageSender
from .auth_handler import Authentication<PERSON>andler
from .connection_manager import ConnectionManager, ConnectionState

logger = get_whatsapp_logger()


class WhatsAppManager:
    """Main manager for WhatsApp integration."""
    
    def __init__(self):
        self.config = get_config()
        self.db_manager = get_database_manager()
        
        # Core components
        self.client: Optional[WhatsAppClient] = None
        self.auth_handler: Optional[AuthenticationHandler] = None
        self.connection_manager: Optional[ConnectionManager] = None
        self.message_listener: Optional[MessageListener] = None
        self.message_sender: Optional[MessageSender] = None
        
        # State
        self.is_initialized = False
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # Event callbacks
        self.qr_code_callback = None
        self.message_callback = None
        self.connection_callback = None
        
        logger.info("WhatsApp manager initialized")
    
    async def initialize(self, ai_engine=None) -> bool:
        """Initialize all WhatsApp components."""
        try:
            if self.is_initialized:
                logger.warning("WhatsApp manager already initialized")
                return True
            
            logger.info("Initializing WhatsApp components...")
            
            # Initialize client
            self.client = WhatsAppClient()
            
            # Initialize authentication handler
            self.auth_handler = AuthenticationHandler(self.client)
            
            # Initialize connection manager
            self.connection_manager = ConnectionManager(self.client, self.auth_handler)
            
            # Initialize message components
            self.message_listener = MessageListener(self.client, ai_engine)
            self.message_sender = MessageSender(self.client)
            
            # Setup callbacks
            self._setup_callbacks()
            
            self.is_initialized = True
            
            # Log initialization
            await self._log_event("whatsapp_initialized", "info", "WhatsApp manager initialized successfully")
            
            logger.info("WhatsApp components initialized successfully")
            return True
            
        except Exception as e:
            import traceback
            logger.error(f"Failed to initialize WhatsApp manager: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            await self._log_event("whatsapp_init_failed", "error", f"Initialization failed: {e}")
            return False
    
    def _setup_callbacks(self):
        """Setup callbacks between components."""
        # Authentication callbacks
        self.auth_handler.set_qr_code_callback(self._handle_qr_code)
        self.auth_handler.set_auth_success_callback(self._handle_auth_success)
        self.auth_handler.set_auth_failure_callback(self._handle_auth_failure)
        
        # Connection callbacks
        self.connection_manager.set_connection_lost_callback(self._handle_connection_lost)
        self.connection_manager.set_connection_restored_callback(self._handle_connection_restored)
        
        # State change callbacks
        self.connection_manager.add_state_change_callback(
            ConnectionState.READY, self._handle_ready_state
        )
        self.connection_manager.add_state_change_callback(
            ConnectionState.ERROR, self._handle_error_state
        )
        
        logger.debug("WhatsApp component callbacks configured")
    
    async def start(self) -> bool:
        """Start the WhatsApp integration."""
        try:
            if not self.is_initialized:
                logger.error("Cannot start: WhatsApp manager not initialized")
                return False
            
            if self.is_running:
                logger.warning("WhatsApp manager already running")
                return True
            
            logger.info("Starting WhatsApp integration...")
            
            # Start connection monitoring
            await self.connection_manager.start_monitoring()
            
            # Start WhatsApp client
            success = await self.client.start()
            
            if success:
                self.is_running = True
                self.start_time = datetime.now()
                
                await self._log_event("whatsapp_started", "info", "WhatsApp integration started")
                logger.info("WhatsApp integration started successfully")
                return True
            else:
                logger.error("Failed to start WhatsApp client")
                await self._log_event("whatsapp_start_failed", "error", "Failed to start WhatsApp client")
                return False
            
        except Exception as e:
            logger.error(f"Error starting WhatsApp integration: {e}")
            await self._log_event("whatsapp_start_error", "error", f"Start error: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the WhatsApp integration."""
        try:
            if not self.is_running:
                logger.warning("WhatsApp manager not running")
                return True
            
            logger.info("Stopping WhatsApp integration...")
            
            # Stop connection monitoring
            if self.connection_manager:
                await self.connection_manager.stop_monitoring()
            
            # Stop WhatsApp client
            if self.client:
                await self.client.stop()
            
            self.is_running = False
            
            await self._log_event("whatsapp_stopped", "info", "WhatsApp integration stopped")
            logger.info("WhatsApp integration stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping WhatsApp integration: {e}")
            await self._log_event("whatsapp_stop_error", "error", f"Stop error: {e}")
            return False
    
    async def restart(self) -> bool:
        """Restart the WhatsApp integration."""
        logger.info("Restarting WhatsApp integration...")
        
        success = await self.stop()
        if success:
            await asyncio.sleep(2)
            success = await self.start()
        
        if success:
            await self._log_event("whatsapp_restarted", "info", "WhatsApp integration restarted")
            logger.info("WhatsApp integration restarted successfully")
        else:
            await self._log_event("whatsapp_restart_failed", "error", "Failed to restart WhatsApp integration")
            logger.error("Failed to restart WhatsApp integration")
        
        return success
    
    async def send_message(self, chat_id: str, message: str, **kwargs) -> bool:
        """Send a message through WhatsApp."""
        if not self.is_running or not self.message_sender:
            logger.error("Cannot send message: WhatsApp not running")
            return False
        
        return await self.message_sender.send_message(chat_id, message, **kwargs)
    
    async def send_message_with_confirmation(self, chat_id: str, message: str, **kwargs) -> bool:
        """Send a message with confirmation requirement."""
        if not self.is_running or not self.message_sender:
            logger.error("Cannot send message: WhatsApp not running")
            return False
        
        return await self.message_sender.send_with_confirmation(chat_id, message, **kwargs)
    
    async def get_chats(self) -> List[Dict[str, Any]]:
        """Get list of WhatsApp chats."""
        if not self.is_running or not self.client:
            logger.error("Cannot get chats: WhatsApp not running")
            return []
        
        await self.client.get_chats()
        # Note: The actual chat data will be received via event handler
        return []
    
    async def get_contacts(self) -> List[Dict[str, Any]]:
        """Get list of WhatsApp contacts."""
        if not self.is_running or not self.client:
            logger.error("Cannot get contacts: WhatsApp not running")
            return []
        
        await self.client.get_contacts()
        # Note: The actual contact data will be received via event handler
        return []
    
    def set_qr_code_callback(self, callback):
        """Set callback for QR code events."""
        self.qr_code_callback = callback
    
    def set_message_callback(self, callback):
        """Set callback for incoming messages."""
        self.message_callback = callback
    
    def set_connection_callback(self, callback):
        """Set callback for connection events."""
        self.connection_callback = callback
    
    async def _handle_qr_code(self, qr_data: str, qr_image_path: Optional[Path] = None):
        """Handle QR code events."""
        logger.info("QR code received for WhatsApp authentication")
        
        await self._log_event("qr_code_received", "info", "QR code generated for authentication")
        
        if self.qr_code_callback:
            try:
                if asyncio.iscoroutinefunction(self.qr_code_callback):
                    await self.qr_code_callback(qr_data, qr_image_path)
                else:
                    self.qr_code_callback(qr_data, qr_image_path)
            except Exception as e:
                logger.error(f"Error in QR code callback: {e}")
    
    async def _handle_auth_success(self):
        """Handle authentication success."""
        logger.info("WhatsApp authentication successful")
        await self._log_event("auth_success", "info", "WhatsApp authentication successful")
    
    async def _handle_auth_failure(self, reason: str):
        """Handle authentication failure."""
        logger.error(f"WhatsApp authentication failed: {reason}")
        await self._log_event("auth_failure", "error", f"Authentication failed: {reason}")
    
    async def _handle_connection_lost(self):
        """Handle connection lost events."""
        logger.warning("WhatsApp connection lost")
        await self._log_event("connection_lost", "warning", "WhatsApp connection lost")
        
        if self.connection_callback:
            try:
                if asyncio.iscoroutinefunction(self.connection_callback):
                    await self.connection_callback("lost")
                else:
                    self.connection_callback("lost")
            except Exception as e:
                logger.error(f"Error in connection callback: {e}")
    
    async def _handle_connection_restored(self):
        """Handle connection restored events."""
        logger.info("WhatsApp connection restored")
        await self._log_event("connection_restored", "info", "WhatsApp connection restored")
        
        if self.connection_callback:
            try:
                if asyncio.iscoroutinefunction(self.connection_callback):
                    await self.connection_callback("restored")
                else:
                    self.connection_callback("restored")
            except Exception as e:
                logger.error(f"Error in connection callback: {e}")
    
    async def _handle_ready_state(self, old_state: ConnectionState, new_state: ConnectionState):
        """Handle ready state changes."""
        logger.info("WhatsApp client is ready")
        await self._log_event("client_ready", "info", "WhatsApp client is ready for use")
    
    async def _handle_error_state(self, old_state: ConnectionState, new_state: ConnectionState):
        """Handle error state changes."""
        logger.error("WhatsApp client entered error state")
        await self._log_event("client_error", "error", "WhatsApp client entered error state")
    
    async def _log_event(self, event_type: str, severity: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Log WhatsApp events to the database."""
        try:
            with self.db_manager.get_session() as session:
                log_entry = SystemLog(
                    event_type=event_type,
                    event_category="whatsapp",
                    severity=severity,
                    message=message,
                    details=details,
                    timestamp=datetime.now()
                )
                session.add(log_entry)
                session.commit()
                
        except Exception as e:
            logger.error(f"Failed to log event to database: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current WhatsApp integration status."""
        status = {
            'initialized': self.is_initialized,
            'running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'client_ready': self.client.is_ready if self.client else False,
            'authenticated': self.auth_handler.is_authenticated if self.auth_handler else False,
            'connection_state': self.connection_manager.state.value if self.connection_manager else 'unknown'
        }
        
        # Add component-specific status
        if self.client:
            status['client_status'] = self.client.get_status()
        
        if self.auth_handler:
            status['auth_status'] = self.auth_handler.get_auth_status()
        
        if self.connection_manager:
            status['connection_info'] = self.connection_manager.get_connection_info()
            status['connection_stats'] = self.connection_manager.get_connection_stats()
        
        return status
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get WhatsApp integration statistics."""
        stats = {}
        
        # Message statistics
        if self.message_listener:
            stats['listener'] = self.message_listener.get_stats()
        
        if self.message_sender:
            stats['sender'] = self.message_sender.get_stats()
        
        # Connection statistics
        if self.connection_manager:
            stats['connection'] = self.connection_manager.get_connection_stats()
        
        # Uptime
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
            stats['uptime_seconds'] = uptime
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on WhatsApp integration."""
        health = {
            'healthy': True,
            'issues': [],
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # Check if initialized
            if not self.is_initialized:
                health['healthy'] = False
                health['issues'].append("Not initialized")
            
            # Check if running
            if not self.is_running:
                health['healthy'] = False
                health['issues'].append("Not running")
            
            # Check client status
            if self.client and not self.client.is_ready:
                health['healthy'] = False
                health['issues'].append("Client not ready")
            
            # Check authentication
            if self.auth_handler and not self.auth_handler.is_authenticated:
                health['issues'].append("Not authenticated")
            
            # Check connection
            if self.connection_manager:
                if self.connection_manager.state != ConnectionState.READY:
                    health['healthy'] = False
                    health['issues'].append(f"Connection state: {self.connection_manager.state.value}")
                
                # Test connection
                if self.connection_manager.state == ConnectionState.READY:
                    connection_test = await self.connection_manager.test_connection()
                    if not connection_test:
                        health['healthy'] = False
                        health['issues'].append("Connection test failed")
            
            return health
            
        except Exception as e:
            logger.error(f"Error during health check: {e}")
            return {
                'healthy': False,
                'issues': [f"Health check error: {e}"],
                'timestamp': datetime.now().isoformat()
            }
