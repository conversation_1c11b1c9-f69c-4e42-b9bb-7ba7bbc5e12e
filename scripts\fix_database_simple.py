#!/usr/bin/env python3
"""
Simple database fix script for WhatsApp AI Assistant.
Removes problematic FTS triggers and tables that cause "no such column: T.contact_id" errors.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from sqlalchemy import text
from data.database import get_database_manager
from utils.logging import get_database_logger

logger = get_database_logger()


def remove_fts_components():
    """Remove FTS triggers and tables that are causing schema issues."""
    db_manager = get_database_manager()
    
    try:
        with db_manager.engine.connect() as conn:
            # Start transaction
            trans = conn.begin()
            
            try:
                logger.info("Removing problematic FTS triggers...")
                
                # Drop all existing FTS triggers
                triggers_to_drop = [
                    'contacts_fts_insert', 'contacts_fts_update', 'contacts_fts_delete',
                    'messages_fts_insert', 'messages_fts_update', 'messages_fts_delete'
                ]
                
                for trigger in triggers_to_drop:
                    try:
                        conn.execute(text(f"DROP TRIGGER IF EXISTS {trigger}"))
                        logger.info(f"Dropped trigger: {trigger}")
                    except Exception as e:
                        logger.warning(f"Could not drop trigger {trigger}: {e}")
                
                logger.info("Removing problematic FTS tables...")
                
                # Drop existing FTS tables that are causing issues
                fts_tables = ['contacts_fts', 'messages_fts']
                
                for table in fts_tables:
                    try:
                        conn.execute(text(f"DROP TABLE IF EXISTS {table}"))
                        logger.info(f"Dropped FTS table: {table}")
                    except Exception as e:
                        logger.warning(f"Could not drop table {table}: {e}")
                
                logger.info("Testing basic database operations...")
                
                # Test basic operations
                result = conn.execute(text("SELECT COUNT(*) FROM contacts"))
                count = result.scalar()
                logger.info(f"Found {count} contacts in database")
                
                # Test update operation that was failing
                if count > 0:
                    conn.execute(text("""
                        UPDATE contacts 
                        SET updated_at = datetime('now') 
                        WHERE id = (SELECT id FROM contacts LIMIT 1)
                    """))
                    logger.info("Test update operation successful")
                
                # Commit transaction
                trans.commit()
                logger.info("Database fix completed successfully")
                return True
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Error fixing database: {e}")
                return False
                
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return False


def verify_fix():
    """Verify that the fix worked."""
    db_manager = get_database_manager()
    
    try:
        with db_manager.engine.connect() as conn:
            # Test basic table access
            result = conn.execute(text("SELECT COUNT(*) FROM contacts"))
            contacts_count = result.scalar()
            logger.info(f"Contacts table has {contacts_count} records")
            
            result = conn.execute(text("SELECT COUNT(*) FROM messages"))
            messages_count = result.scalar()
            logger.info(f"Messages table has {messages_count} records")
            
            # Test update operation
            if contacts_count > 0:
                conn.execute(text("BEGIN"))
                conn.execute(text("UPDATE contacts SET updated_at = datetime('now') WHERE id = 1"))
                conn.execute(text("ROLLBACK"))
                logger.info("Update operation test successful")
            
            logger.info("Database verification completed successfully")
            return True
            
    except Exception as e:
        logger.error(f"Database verification failed: {e}")
        return False


def main():
    """Main function."""
    print("WhatsApp AI Assistant - Simple Database Fix")
    print("=" * 50)
    
    print("Removing problematic FTS components...")
    if remove_fts_components():
        print("✅ Database fix completed successfully")
    else:
        print("❌ Database fix failed")
        return False
    
    print("Verifying fix...")
    if verify_fix():
        print("✅ Database verification successful")
        return True
    else:
        print("❌ Database verification failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
