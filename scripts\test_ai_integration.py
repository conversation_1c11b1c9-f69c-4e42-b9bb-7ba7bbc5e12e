#!/usr/bin/env python3
"""
Test AI engine integration with the conversation manager.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from integrations.telegram_conversation_manager import TelegramConversationManager
from core.ai_engine import AIEngine
from utils.logging import get_logger

logger = get_logger("test_ai_integration")


async def test_ai_engine_integration():
    """Test the AI engine integration with conversation manager."""
    print("🤖 Testing AI Engine Integration...")
    
    try:
        # Initialize AI engine
        print("   Initializing AI engine...")
        ai_engine = AIEngine()
        await ai_engine.initialize()
        await ai_engine.start()
        print("   ✅ AI engine initialized and started")
        
        # Initialize conversation manager with AI engine
        print("   Initializing conversation manager with AI engine...")
        conversation_manager = TelegramConversationManager(ai_engine=ai_engine)
        await conversation_manager.initialize()
        print("   ✅ Conversation manager initialized with AI engine")
        
        # Test AI engine directly
        print("\n   Testing AI engine directly...")
        test_user_id = 12345
        test_message = "Hello, can you help me analyze my WhatsApp messages?"
        
        ai_result = await ai_engine.process_message(
            contact_id=test_user_id,
            message_text=test_message,
            chat_id=f"telegram_{test_user_id}",
            force_response=True
        )
        
        print(f"   AI Engine Result Type: {type(ai_result)}")
        if isinstance(ai_result, dict):
            print(f"   AI Engine Result Keys: {list(ai_result.keys())}")
            if 'response' in ai_result:
                print(f"   AI Response: {ai_result['response'][:100]}...")
            elif 'ai_response' in ai_result:
                print(f"   AI Response: {ai_result['ai_response'][:100]}...")
        elif isinstance(ai_result, str):
            print(f"   AI Response: {ai_result[:100]}...")
        
        # Test conversation manager with AI engine
        print("\n   Testing conversation manager with AI engine...")
        cm_response = await conversation_manager.process_message(test_user_id, test_message)
        print(f"   Conversation Manager Response: {cm_response[:100]}...")
        
        # Test different types of queries
        test_queries = [
            "How many messages do I have?",
            "Who are my most active contacts?",
            "Show me recent messages",
            "What can you tell me about my messaging patterns?"
        ]
        
        print("\n   Testing various queries...")
        for i, query in enumerate(test_queries, 1):
            print(f"   Query {i}: {query}")
            response = await conversation_manager.process_message(test_user_id + i, query)
            
            # Check if response looks like AI-generated or fallback
            if "I'm your WhatsApp AI Assistant" in response:
                print(f"   ⚠️  Fallback response detected")
            else:
                print(f"   ✅ AI response: {response[:80]}...")
        
        # Check conversation stats
        stats = conversation_manager.get_conversation_stats()
        print(f"\n   📊 Final stats: {stats['active_conversations']} conversations, {stats['total_memory_messages']} messages")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        logger.error(f"AI integration test failed: {e}")
        return False


async def test_ai_engine_methods():
    """Test AI engine methods and responses."""
    print("\n🔍 Testing AI Engine Methods...")
    
    try:
        ai_engine = AIEngine()
        await ai_engine.initialize()
        await ai_engine.start()
        
        # Test with a simple message
        print("   Testing simple message processing...")
        result = await ai_engine.process_message(
            contact_id=99999,
            message_text="Hello, how are you?",
            chat_id="test_chat",
            force_response=True
        )
        
        print(f"   Result type: {type(result)}")
        print(f"   Result content: {str(result)[:200]}...")
        
        # Test with WhatsApp-related query
        print("   Testing WhatsApp query...")
        result2 = await ai_engine.process_message(
            contact_id=99999,
            message_text="Can you analyze my WhatsApp message patterns?",
            chat_id="test_chat",
            force_response=True
        )
        
        print(f"   Result type: {type(result2)}")
        print(f"   Result content: {str(result2)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI engine methods test failed: {e}")
        logger.error(f"AI engine methods test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting AI Integration Tests\n")
    
    # Test 1: AI engine integration
    test1_success = await test_ai_engine_integration()
    
    # Test 2: AI engine methods
    test2_success = await test_ai_engine_methods()
    
    # Summary
    print(f"\n📋 Test Summary:")
    print(f"   AI Integration: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   AI Engine Methods: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All AI integration tests passed!")
        return 0
    else:
        print("\n💥 Some AI integration tests failed. Check the logs for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
