#!/usr/bin/env python3
"""
Test script for the Telegram Conversation Manager.
Tests the AI conversation functionality without requiring Telegram.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from integrations.telegram_conversation_manager import TelegramConversationManager
from utils.logging import get_logger

logger = get_logger("test_conversation")


async def test_conversation_manager():
    """Test the conversation manager functionality."""
    print("🧪 Testing Telegram Conversation Manager...")
    
    try:
        # Initialize conversation manager
        print("📝 Initializing conversation manager...")
        conversation_manager = TelegramConversationManager()
        
        # Initialize the manager
        success = await conversation_manager.initialize()
        if not success:
            print("❌ Failed to initialize conversation manager")
            return False
        
        print("✅ Conversation manager initialized successfully")
        
        # Test basic functionality
        print("\n📊 Testing basic stats...")
        stats = conversation_manager.get_conversation_stats()
        print(f"   Active conversations: {stats['active_conversations']}")
        print(f"   Cache size: {stats['cache_size']}")
        print(f"   Last cache update: {stats['last_cache_update']}")
        
        # Test message processing
        print("\n💬 Testing message processing...")
        test_user_id = 12345
        
        # Test 1: Basic greeting
        print("   Test 1: Basic greeting")
        response = await conversation_manager.process_message(test_user_id, "Hello!")
        print(f"   Response: {response[:100]}...")
        
        # Test 2: Statistics request
        print("   Test 2: Statistics request")
        response = await conversation_manager.process_message(test_user_id, "Show me my message statistics")
        print(f"   Response: {response[:100]}...")
        
        # Test 3: Contact query
        print("   Test 3: Contact query")
        response = await conversation_manager.process_message(test_user_id, "Who are my contacts?")
        print(f"   Response: {response[:100]}...")
        
        # Test 4: Recent messages
        print("   Test 4: Recent messages")
        response = await conversation_manager.process_message(test_user_id, "Show me recent messages")
        print(f"   Response: {response[:100]}...")
        
        # Test conversation memory
        print("\n🧠 Testing conversation memory...")
        stats_after = conversation_manager.get_conversation_stats()
        print(f"   Active conversations: {stats_after['active_conversations']}")
        print(f"   Total memory messages: {stats_after['total_memory_messages']}")
        
        # Test multiple users
        print("\n👥 Testing multiple users...")
        test_user_2 = 67890
        response = await conversation_manager.process_message(test_user_2, "Hi there!")
        print(f"   User 2 response: {response[:100]}...")
        
        stats_multi = conversation_manager.get_conversation_stats()
        print(f"   Active conversations: {stats_multi['active_conversations']}")
        print(f"   Total memory messages: {stats_multi['total_memory_messages']}")
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.error(f"Test failed: {e}")
        return False


async def test_whatsapp_data_access():
    """Test WhatsApp data access functionality."""
    print("\n📱 Testing WhatsApp data access...")
    
    try:
        conversation_manager = TelegramConversationManager()
        await conversation_manager.initialize()
        
        # Test data access methods
        print("   Testing recent messages...")
        recent_messages = await conversation_manager._get_recent_messages(5)
        print(f"   Found {len(recent_messages)} recent messages")
        
        print("   Testing active conversations...")
        active_conversations = await conversation_manager._get_active_conversations()
        print(f"   Found {len(active_conversations)} active conversations")
        
        print("   Testing message statistics...")
        stats = await conversation_manager._get_message_statistics()
        print(f"   Total messages: {stats.get('total_messages', 0)}")
        print(f"   Messages today: {stats.get('messages_today', 0)}")
        print(f"   Unique contacts: {stats.get('unique_contacts', 0)}")
        
        print("   Testing contact search...")
        mentioned_contacts = await conversation_manager._find_mentioned_contacts("test message")
        print(f"   Found {len(mentioned_contacts)} mentioned contacts")
        
        print("✅ WhatsApp data access tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ WhatsApp data access test failed: {e}")
        logger.error(f"WhatsApp data access test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting Conversation Manager Tests\n")
    
    # Test 1: Basic conversation manager functionality
    test1_success = await test_conversation_manager()
    
    # Test 2: WhatsApp data access
    test2_success = await test_whatsapp_data_access()
    
    # Summary
    print(f"\n📋 Test Summary:")
    print(f"   Conversation Manager: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   WhatsApp Data Access: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! The conversation manager is working correctly.")
        return 0
    else:
        print("\n💥 Some tests failed. Check the logs for more details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
