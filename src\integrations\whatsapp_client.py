"""
WhatsApp Client Integration for WhatsApp AI Assistant.
Python wrapper for whatsapp-web.js via child process communication.
"""

import asyncio
import json
import subprocess
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from queue import Queue, Empty
import signal
import os

from utils.logging import get_whatsapp_logger
from utils.config import get_config

logger = get_whatsapp_logger()


class WhatsAppClient:
    """Python wrapper for WhatsApp Web.js client."""
    
    def __init__(self):
        self.config = get_config()
        self.process: Optional[subprocess.Popen] = None
        self.is_ready = False
        self.is_authenticated = False
        self.message_queue = Queue()
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.command_responses: Dict[str, Any] = {}
        self.running = False
        
        # Paths
        self.project_root = Path(__file__).parent.parent.parent
        self.js_client_path = self.project_root / "js" / "whatsapp_client.js"
        
        # Threading
        self.reader_thread: Optional[threading.Thread] = None
        self.writer_thread: Optional[threading.Thread] = None
        
        logger.info("WhatsApp client initialized")
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """Add an event handler for specific WhatsApp events."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        logger.debug(f"Added event handler for {event_type}")
    
    def remove_event_handler(self, event_type: str, handler: Callable):
        """Remove an event handler."""
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
                logger.debug(f"Removed event handler for {event_type}")
            except ValueError:
                logger.warning(f"Handler not found for {event_type}")
    
    async def start(self) -> bool:
        """Start the WhatsApp client process."""
        try:
            logger.info("Starting WhatsApp Web.js client...")

            # Check if Node.js client exists
            if not self.js_client_path.exists():
                logger.error(f"WhatsApp client script not found: {self.js_client_path}")
                return False

            # Clean up any existing process
            if self.process and self.process.poll() is None:
                logger.info("Terminating existing WhatsApp client process...")
                self.process.terminate()
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("Force killing WhatsApp client process...")
                    self.process.kill()
                    self.process.wait()

            # Set environment variables for better browser stability
            env = os.environ.copy()
            env.update({
                'NODE_ENV': 'production',
                'PUPPETEER_DISABLE_HEADLESS_WARNING': 'true',
                'PUPPETEER_SKIP_CHROMIUM_DOWNLOAD': 'false',
                'DISPLAY': ':99' if os.name != 'nt' else env.get('DISPLAY', ''),
            })

            # Start the Node.js process with explicit UTF-8 encoding
            self.process = subprocess.Popen(
                ["node", str(self.js_client_path)],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',  # Replace invalid characters instead of failing
                bufsize=1,
                cwd=str(self.project_root),
                env=env
            )
            
            self.running = True
            
            # Start reader and writer threads
            self.reader_thread = threading.Thread(target=self._read_output, daemon=True)
            self.writer_thread = threading.Thread(target=self._write_commands, daemon=True)
            
            self.reader_thread.start()
            self.writer_thread.start()
            
            logger.info("WhatsApp client process started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start WhatsApp client: {e}")
            return False
    
    def _read_output(self):
        """Read output from the Node.js process."""
        try:
            while self.running and self.process and self.process.poll() is None:
                try:
                    line = self.process.stdout.readline()
                    if line:
                        try:
                            # Log the raw output for debugging
                            raw_output = line.strip()
                            if "MESSAGE RECEIVED" in raw_output or "MESSAGE SENT" in raw_output:
                                logger.info(f"Raw JS output: {raw_output}")
                            
                            # Try to parse as JSON
                            if raw_output.startswith('{') and raw_output.endswith('}'):
                                data = json.loads(raw_output)
                                self._handle_event(data)
                            else:
                                # Handle non-JSON output
                                if "MESSAGE RECEIVED" in raw_output or "MESSAGE SENT" in raw_output:
                                    logger.info(f"Message log from JS: {raw_output}")
                                else:
                                    logger.debug(f"Non-JSON output from Node.js: {raw_output}")
                        except json.JSONDecodeError as e:
                            # Don't log QR code ASCII art and other non-JSON output as warnings
                            if "MESSAGE RECEIVED" in line or "MESSAGE SENT" in line:
                                logger.info(f"Message log (non-JSON): {line.strip()}")
                            else:
                                logger.debug(f"Non-JSON output from Node.js: {line.strip()}")
                    else:
                        time.sleep(0.1)
                except UnicodeDecodeError as ude:
                    # Handle Unicode decode errors gracefully
                    logger.warning(f"Unicode decode error in Node.js output: {ude}")
        except Exception as e:
            logger.error(f"Error reading from Node.js process: {e}")
        finally:
            logger.debug("Reader thread stopped")
    
    def _write_commands(self):
        """Write commands to the Node.js process."""
        try:
            while self.running and self.process and self.process.poll() is None:
                try:
                    command = self.message_queue.get(timeout=1.0)
                    if command:
                        command_json = json.dumps(command) + '\n'
                        self.process.stdin.write(command_json)
                        self.process.stdin.flush()
                        logger.debug(f"Sent command: {command['type']}")
                except Empty:
                    continue
                except Exception as e:
                    logger.error(f"Error writing command: {e}")
        except Exception as e:
            logger.error(f"Error in writer thread: {e}")
        finally:
            logger.debug("Writer thread stopped")
    
    def _handle_event(self, data: Dict[str, Any]):
        """Handle events from the Node.js process."""
        event_type = data.get('type')
        event_data = data.get('data')

        logger.debug(f"Received event: {event_type}")

        # Update internal state
        if event_type == 'ready':
            self.is_ready = True
            logger.info("WhatsApp client is ready")
            print("\n" + "="*60)
            print("✅ WHATSAPP CLIENT READY!")
            print("="*60)
            print("🎉 WhatsApp is now connected and ready to use!")
            print("📱 You can now send and receive messages")
            print("="*60 + "\n")
        elif event_type == 'authenticated':
            self.is_authenticated = True
            logger.info("WhatsApp authentication successful")
            print("\n" + "="*60)
            print("🔐 WHATSAPP AUTHENTICATION SUCCESSFUL!")
            print("="*60)
            print("✅ Your device has been successfully linked")
            print("⏳ Waiting for client to be ready...")
            print("="*60 + "\n")
        elif event_type == 'auth_failure':
            self.is_authenticated = False
            logger.error(f"WhatsApp authentication failed: {event_data}")
            print("\n" + "="*60)
            print("❌ WHATSAPP AUTHENTICATION FAILED!")
            print("="*60)
            print(f"Error: {event_data}")
            print("🔄 Please scan the QR code again")
            print("="*60 + "\n")
        elif event_type == 'disconnected':
            self.is_ready = False
            self.is_authenticated = False
            logger.warning(f"WhatsApp client disconnected: {event_data}")
            print("\n" + "="*60)
            print("⚠️  WHATSAPP CLIENT DISCONNECTED!")
            print("="*60)
            print(f"Reason: {event_data}")
            print("🔄 Attempting to reconnect...")
            print("="*60 + "\n")
        elif event_type == 'qr_code':
            logger.info("QR code received for WhatsApp authentication")
        elif event_type == 'message':
            # Enhanced message logging
            from_me = event_data.get('fromMe', False)
            from_id = event_data.get('from', 'unknown')
            body = event_data.get('body', '')
            
            # Log with high visibility
            if from_me:
                logger.info(f"Message event: Sent message to {from_id}")
            else:
                logger.info(f"Message event: Received message from {from_id}")
            
            # Log message content
            logger.info(f"Message content: {body[:100]}{'...' if len(body) > 100 else ''}")
        elif event_type == 'error':
            logger.error(f"Error from Node.js client: {event_data}")

        # Call registered event handlers
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        # Schedule async handler properly
                        self._schedule_async_handler(handler, event_data)
                    else:
                        # Call sync handler
                        handler(event_data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event_type}: {e}")

    def _schedule_async_handler(self, handler: Callable, event_data: Any):
        """Schedule an async handler to run in the event loop."""
        try:
            # Try to get the current event loop
            try:
                loop = asyncio.get_running_loop()
                # If loop is running, schedule the coroutine
                asyncio.create_task(handler(event_data))
            except RuntimeError:
                # No event loop in current thread, run in new thread
                import threading
                def run_async():
                    try:
                        asyncio.run(handler(event_data))
                    except Exception as e:
                        logger.error(f"Error in async handler thread: {e}")
                thread = threading.Thread(target=run_async, daemon=True)
                thread.start()
        except Exception as e:
            logger.error(f"Error scheduling async handler: {e}")
    
    def _send_command(self, command_type: str, data: Optional[Dict[str, Any]] = None):
        """Send a command to the Node.js process."""
        command = {
            'type': command_type,
            'data': data or {}
        }
        
        if self.running:
            self.message_queue.put(command)
        else:
            logger.warning(f"Cannot send command {command_type}: client not running")
    
    async def send_message(self, chat_id: str, message: str, options: Optional[Dict[str, Any]] = None) -> bool:
        """Send a message to a WhatsApp chat."""
        if not self.is_ready:
            logger.error("Cannot send message: WhatsApp client not ready")
            return False
        
        try:
            self._send_command('send_message', {
                'chatId': chat_id,
                'message': message,
                'options': options or {}
            })
            
            logger.info(f"Message sent to {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    async def get_chats(self):
        """Get list of all chats."""
        if not self.is_ready:
            logger.error("Cannot get chats: WhatsApp client not ready")
            return []
        
        self._send_command('get_chats')
    
    async def get_contacts(self):
        """Get list of all contacts."""
        if not self.is_ready:
            logger.error("Cannot get contacts: WhatsApp client not ready")
            return []
        
        self._send_command('get_contacts')
    
    async def get_chat_by_id(self, chat_id: str):
        """Get specific chat by ID."""
        if not self.is_ready:
            logger.error("Cannot get chat: WhatsApp client not ready")
            return None
        
        self._send_command('get_chat_by_id', {'chatId': chat_id})
    
    async def mark_chat_unread(self, chat_id: str):
        """Mark a chat as unread."""
        self._send_command('mark_chat_unread', {'chatId': chat_id})
    
    async def archive_chat(self, chat_id: str):
        """Archive a chat."""
        self._send_command('archive_chat', {'chatId': chat_id})
    
    async def pin_chat(self, chat_id: str):
        """Pin a chat."""
        self._send_command('pin_chat', {'chatId': chat_id})
    
    async def mute_chat(self, chat_id: str, duration: Optional[int] = None):
        """Mute a chat for specified duration (in seconds)."""
        self._send_command('mute_chat', {
            'chatId': chat_id,
            'duration': duration
        })
    
    async def get_profile_pic(self, contact_id: str):
        """Get profile picture URL for a contact."""
        self._send_command('get_profile_pic', {'contactId': contact_id})
    
    async def set_status(self, status: str):
        """Set WhatsApp status."""
        self._send_command('set_status', {'status': status})
    
    async def logout(self):
        """Logout from WhatsApp."""
        self._send_command('logout')
        self.is_authenticated = False
        self.is_ready = False
    
    async def restart(self):
        """Restart the WhatsApp client."""
        logger.info("Restarting WhatsApp client...")
        await self.stop()
        await asyncio.sleep(2)
        await self.start()
    
    async def stop(self):
        """Stop the WhatsApp client."""
        logger.info("Stopping WhatsApp client...")
        
        self.running = False
        
        if self.process:
            try:
                # Send shutdown command
                self._send_command('logout')
                
                # Wait for graceful shutdown
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("Process didn't terminate gracefully, forcing...")
                    self.process.terminate()
                    try:
                        self.process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        logger.error("Process didn't terminate, killing...")
                        self.process.kill()
                
                self.process = None
                
            except Exception as e:
                logger.error(f"Error stopping WhatsApp client: {e}")
        
        # Wait for threads to finish
        if self.reader_thread and self.reader_thread.is_alive():
            self.reader_thread.join(timeout=2)
        
        if self.writer_thread and self.writer_thread.is_alive():
            self.writer_thread.join(timeout=2)
        
        self.is_ready = False
        self.is_authenticated = False
        
        logger.info("WhatsApp client stopped")

    async def force_cleanup(self):
        """Force cleanup of client resources and session data."""
        try:
            logger.info("Performing force cleanup...")

            # Kill any remaining processes
            if self.process:
                try:
                    self.process.kill()
                    self.process.wait()
                except Exception as e:
                    logger.warning(f"Error killing process: {e}")
                finally:
                    self.process = None

            # Clear session data
            await self.clear_session()

            # Reset state
            self.is_ready = False
            self.is_authenticated = False
            self.running = False

            logger.info("Force cleanup completed")

        except Exception as e:
            logger.error(f"Error during force cleanup: {e}")

    async def clear_session(self):
        """Clear WhatsApp session data."""
        try:
            logger.info("Clearing WhatsApp session data...")

            # Session path is typically in the js directory
            session_path = self.project_root / "js" / ".wwebjs_auth"

            if session_path.exists():
                import shutil
                shutil.rmtree(session_path, ignore_errors=True)
                logger.info("Session data cleared")
            else:
                logger.debug("No session data found to clear")

        except Exception as e:
            logger.warning(f"Error clearing session data: {e}")

    def is_running(self) -> bool:
        """Check if the client is running."""
        return self.running and self.process and self.process.poll() is None
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the WhatsApp client."""
        return {
            'running': self.is_running(),
            'ready': self.is_ready,
            'authenticated': self.is_authenticated,
            'process_id': self.process.pid if self.process else None
        }
