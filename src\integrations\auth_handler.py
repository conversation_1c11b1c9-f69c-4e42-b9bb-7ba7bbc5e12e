"""
Authentication Handler for WhatsApp AI Assistant.
Handles QR code authentication and session management for WhatsApp Web.
"""

import asyncio
import base64
import qrcode
import io
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, Callable
import json
import os

from utils.logging import get_whatsapp_logger
from utils.config import get_config
from utils.helpers import FileHelper

logger = get_whatsapp_logger()


class AuthenticationHandler:
    """Handles WhatsApp Web authentication and session management."""
    
    def __init__(self, whatsapp_client):
        self.whatsapp_client = whatsapp_client
        self.config = get_config()
        
        # Authentication state
        self.is_authenticated = False
        self.authentication_pending = False
        self.qr_code_data = None
        self.session_valid = False
        
        # Paths
        self.session_path = Path(self.config.whatsapp.session_path)
        self.qr_code_path = Path("data/qr_codes")
        
        # Callbacks
        self.qr_code_callback: Optional[Callable] = None
        self.auth_success_callback: Optional[Callable] = None
        self.auth_failure_callback: Optional[Callable] = None
        
        # Session management
        self.session_timeout = timedelta(hours=24)  # Session validity check interval
        self.last_session_check = None
        
        # Setup directories
        self._setup_directories()
        
        # Setup event handlers
        self._setup_handlers()
        
        logger.info("Authentication handler initialized")
    
    def _setup_directories(self):
        """Create necessary directories for session and QR code storage."""
        try:
            FileHelper.ensure_directory(self.session_path)
            FileHelper.ensure_directory(self.qr_code_path)
            logger.debug("Authentication directories created")
        except Exception as e:
            logger.error(f"Error creating authentication directories: {e}")
    
    def _setup_handlers(self):
        """Setup event handlers for authentication events."""
        self.whatsapp_client.add_event_handler('qr_code', self._handle_qr_code)
        self.whatsapp_client.add_event_handler('authenticated', self._handle_authenticated)
        self.whatsapp_client.add_event_handler('auth_failure', self._handle_auth_failure)
        self.whatsapp_client.add_event_handler('ready', self._handle_ready)
        self.whatsapp_client.add_event_handler('disconnected', self._handle_disconnected)
        
        logger.debug("Authentication event handlers registered")
    
    async def _handle_qr_code(self, qr_data: str):
        """Handle QR code generation event."""
        try:
            self.qr_code_data = qr_data
            self.authentication_pending = True
            self.is_authenticated = False
            
            logger.info("QR code received for WhatsApp authentication")
            
            # Generate QR code image
            qr_image_path = await self._generate_qr_image(qr_data)

            # Display QR code in terminal
            await self._display_qr_code_terminal(qr_data)

            # Save QR code data
            await self._save_qr_data(qr_data)

            # Print instructions
            print("\n" + "="*60)
            print("🔗 WHATSAPP QR CODE AUTHENTICATION")
            print("="*60)
            print("📱 Open WhatsApp on your phone")
            print("⚙️  Go to Settings > Linked Devices")
            print("➕ Tap 'Link a Device'")
            print("📷 Scan the QR code above")
            print("="*60)
            print(f"📁 QR code also saved to: {qr_image_path}")
            print("="*60 + "\n")

            # Call callback if registered
            if self.qr_code_callback:
                try:
                    if asyncio.iscoroutinefunction(self.qr_code_callback):
                        await self.qr_code_callback(qr_data, qr_image_path)
                    else:
                        self.qr_code_callback(qr_data, qr_image_path)
                except Exception as e:
                    logger.error(f"Error in QR code callback: {e}")

            logger.debug("QR code processing completed")
            
        except Exception as e:
            logger.error(f"Error handling QR code: {e}")
    
    async def _handle_authenticated(self, auth_data: Any):
        """Handle successful authentication event."""
        try:
            self.is_authenticated = True
            self.authentication_pending = False
            self.session_valid = True
            self.last_session_check = datetime.now()
            
            logger.info("WhatsApp authentication successful")
            
            # Clean up QR code files
            await self._cleanup_qr_files()
            
            # Save authentication timestamp
            await self._save_auth_info()
            
            # Call callback if registered
            if self.auth_success_callback:
                try:
                    if asyncio.iscoroutinefunction(self.auth_success_callback):
                        await self.auth_success_callback()
                    else:
                        self.auth_success_callback()
                except Exception as e:
                    logger.error(f"Error in auth success callback: {e}")
            
        except Exception as e:
            logger.error(f"Error handling authentication success: {e}")
    
    async def _handle_auth_failure(self, failure_reason: str):
        """Handle authentication failure event."""
        try:
            self.is_authenticated = False
            self.authentication_pending = False
            self.session_valid = False
            
            logger.error(f"WhatsApp authentication failed: {failure_reason}")
            
            # Clean up QR code files
            await self._cleanup_qr_files()
            
            # Call callback if registered
            if self.auth_failure_callback:
                try:
                    if asyncio.iscoroutinefunction(self.auth_failure_callback):
                        await self.auth_failure_callback(failure_reason)
                    else:
                        self.auth_failure_callback(failure_reason)
                except Exception as e:
                    logger.error(f"Error in auth failure callback: {e}")
            
        except Exception as e:
            logger.error(f"Error handling authentication failure: {e}")
    
    async def _handle_ready(self, ready_data: Any):
        """Handle client ready event."""
        try:
            self.session_valid = True
            self.last_session_check = datetime.now()
            
            logger.info("WhatsApp client is ready and session is valid")
            
        except Exception as e:
            logger.error(f"Error handling ready event: {e}")
    
    async def _handle_disconnected(self, disconnect_reason: str):
        """Handle disconnection event."""
        try:
            self.session_valid = False
            
            logger.warning(f"WhatsApp client disconnected: {disconnect_reason}")
            
            # Check if we need to re-authenticate
            if not self.is_authenticated:
                logger.info("Will need to re-authenticate on next connection")
            
        except Exception as e:
            logger.error(f"Error handling disconnection: {e}")
    
    async def _display_qr_code_terminal(self, qr_data: str):
        """Display QR code in terminal."""
        try:
            # Create QR code for terminal display
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=1,
                border=2,
            )
            qr.add_data(qr_data)
            qr.make(fit=True)

            # Print QR code to terminal
            print("\n" + "="*50)
            print("📱 SCAN THIS QR CODE WITH WHATSAPP")
            print("="*50)
            qr.print_ascii(invert=True)
            print("="*50)

        except Exception as e:
            logger.error(f"Error displaying QR code in terminal: {e}")
            # Fallback: show QR data as text
            print(f"\nQR Code Data: {qr_data[:50]}...")

    async def _generate_qr_image(self, qr_data: str) -> Path:
        """Generate QR code image from data."""
        try:
            # Create QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_data)
            qr.make(fit=True)

            # Create image
            img = qr.make_image(fill_color="black", back_color="white")

            # Save image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            qr_image_path = self.qr_code_path / f"whatsapp_qr_{timestamp}.png"

            img.save(qr_image_path)

            logger.debug(f"QR code image saved: {qr_image_path}")
            return qr_image_path

        except Exception as e:
            logger.error(f"Error generating QR code image: {e}")
            return None
    
    async def _save_qr_data(self, qr_data: str):
        """Save QR code data to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            qr_data_path = self.qr_code_path / f"whatsapp_qr_{timestamp}.txt"
            
            with open(qr_data_path, 'w') as f:
                f.write(qr_data)
            
            logger.debug(f"QR code data saved: {qr_data_path}")
            
        except Exception as e:
            logger.error(f"Error saving QR code data: {e}")
    
    async def _cleanup_qr_files(self):
        """Clean up old QR code files."""
        try:
            if self.qr_code_path.exists():
                for qr_file in self.qr_code_path.glob("whatsapp_qr_*"):
                    try:
                        qr_file.unlink()
                        logger.debug(f"Cleaned up QR file: {qr_file}")
                    except Exception as e:
                        logger.warning(f"Could not delete QR file {qr_file}: {e}")
            
        except Exception as e:
            logger.error(f"Error cleaning up QR files: {e}")
    
    async def _save_auth_info(self):
        """Save authentication information."""
        try:
            auth_info = {
                'authenticated_at': datetime.now().isoformat(),
                'session_valid': self.session_valid,
                'last_check': self.last_session_check.isoformat() if self.last_session_check else None
            }
            
            auth_info_path = self.session_path / "auth_info.json"
            
            with open(auth_info_path, 'w') as f:
                json.dump(auth_info, f, indent=2)
            
            logger.debug("Authentication info saved")
            
        except Exception as e:
            logger.error(f"Error saving authentication info: {e}")
    
    async def load_auth_info(self) -> Dict[str, Any]:
        """Load saved authentication information."""
        try:
            auth_info_path = self.session_path / "auth_info.json"
            
            if auth_info_path.exists():
                with open(auth_info_path, 'r') as f:
                    auth_info = json.load(f)
                
                # Parse timestamps
                if auth_info.get('authenticated_at'):
                    auth_info['authenticated_at'] = datetime.fromisoformat(auth_info['authenticated_at'])
                
                if auth_info.get('last_check'):
                    auth_info['last_check'] = datetime.fromisoformat(auth_info['last_check'])
                    self.last_session_check = auth_info['last_check']
                
                logger.debug("Authentication info loaded")
                return auth_info
            
            return {}
            
        except Exception as e:
            logger.error(f"Error loading authentication info: {e}")
            return {}
    
    def set_qr_code_callback(self, callback: Callable):
        """Set callback for QR code events."""
        self.qr_code_callback = callback
        logger.debug("QR code callback registered")
    
    def set_auth_success_callback(self, callback: Callable):
        """Set callback for authentication success events."""
        self.auth_success_callback = callback
        logger.debug("Auth success callback registered")
    
    def set_auth_failure_callback(self, callback: Callable):
        """Set callback for authentication failure events."""
        self.auth_failure_callback = callback
        logger.debug("Auth failure callback registered")
    
    async def check_session_validity(self) -> bool:
        """Check if the current session is still valid."""
        try:
            if not self.last_session_check:
                return False
            
            # Check if session check is due
            time_since_check = datetime.now() - self.last_session_check
            if time_since_check < self.session_timeout:
                return self.session_valid
            
            # Update last check time
            self.last_session_check = datetime.now()
            
            # Check if client is ready (indicates valid session)
            if self.whatsapp_client.is_ready:
                self.session_valid = True
                await self._save_auth_info()
                return True
            else:
                self.session_valid = False
                return False
            
        except Exception as e:
            logger.error(f"Error checking session validity: {e}")
            return False
    
    async def logout(self):
        """Logout from WhatsApp and clear session."""
        try:
            logger.info("Logging out from WhatsApp...")
            
            # Send logout command to client
            await self.whatsapp_client.logout()
            
            # Update state
            self.is_authenticated = False
            self.session_valid = False
            self.authentication_pending = False
            
            # Clean up session files
            await self._cleanup_session_files()
            
            logger.info("WhatsApp logout completed")
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
    
    async def _cleanup_session_files(self):
        """Clean up session files."""
        try:
            logger.info("Cleaning up WhatsApp session files...")

            # Clean up main session directory
            if self.session_path.exists():
                for session_file in self.session_path.glob("*"):
                    if session_file.is_file():
                        try:
                            session_file.unlink()
                            logger.debug(f"Cleaned up session file: {session_file}")
                        except Exception as e:
                            logger.warning(f"Could not delete session file {session_file}: {e}")
                    elif session_file.is_dir():
                        try:
                            import shutil
                            shutil.rmtree(session_file)
                            logger.debug(f"Cleaned up session directory: {session_file}")
                        except Exception as e:
                            logger.warning(f"Could not delete session directory {session_file}: {e}")

            # Also clean up the JS client session directory
            js_session_path = Path("js/.wwebjs_auth")
            if js_session_path.exists():
                try:
                    import shutil
                    shutil.rmtree(js_session_path, ignore_errors=True)
                    logger.debug("Cleaned up JS client session directory")
                except Exception as e:
                    logger.warning(f"Could not clean up JS session directory: {e}")

            # Clean up Chrome user data if it exists
            chrome_data_paths = [
                Path("js/.wwebjs_cache"),
                Path("js/session"),
                Path("js/Default"),
                Path("js/SingletonLock"),
                Path("js/SingletonSocket"),
                Path("js/SingletonCookie")
            ]

            for chrome_path in chrome_data_paths:
                if chrome_path.exists():
                    try:
                        if chrome_path.is_file():
                            chrome_path.unlink()
                        else:
                            import shutil
                            shutil.rmtree(chrome_path, ignore_errors=True)
                        logger.debug(f"Cleaned up Chrome data: {chrome_path}")
                    except Exception as e:
                        logger.debug(f"Could not clean up Chrome data {chrome_path}: {e}")

            logger.info("Session cleanup completed")

        except Exception as e:
            logger.error(f"Error cleaning up session files: {e}")
    
    def get_auth_status(self) -> Dict[str, Any]:
        """Get current authentication status."""
        return {
            'is_authenticated': self.is_authenticated,
            'authentication_pending': self.authentication_pending,
            'session_valid': self.session_valid,
            'has_qr_code': self.qr_code_data is not None,
            'last_session_check': self.last_session_check.isoformat() if self.last_session_check else None
        }
    
    async def force_reauth(self):
        """Force re-authentication by clearing session."""
        try:
            logger.info("Forcing re-authentication...")

            # Logout first
            await self.logout()

            # Wait a moment
            await asyncio.sleep(2)

            # Restart client to trigger new authentication
            await self.whatsapp_client.restart()

            logger.info("Re-authentication initiated")

        except Exception as e:
            logger.error(f"Error forcing re-authentication: {e}")

    async def detect_corrupted_session(self) -> bool:
        """Detect if the current session is corrupted."""
        try:
            # Check for common signs of session corruption
            corruption_indicators = []

            # Check if session files exist but client can't authenticate
            if self.session_path.exists():
                session_files = list(self.session_path.glob("*"))
                if session_files and not self.is_authenticated:
                    corruption_indicators.append("Session files exist but not authenticated")

            # Check for incomplete session files
            js_session_path = Path("js/.wwebjs_auth")
            if js_session_path.exists():
                try:
                    session_dirs = list(js_session_path.glob("session-*"))
                    for session_dir in session_dirs:
                        # Check if session directory is empty or has incomplete files
                        if session_dir.is_dir():
                            session_files = list(session_dir.glob("*"))
                            if len(session_files) < 3:  # Minimum expected files
                                corruption_indicators.append(f"Incomplete session directory: {session_dir}")
                except Exception as e:
                    corruption_indicators.append(f"Error reading session directory: {e}")

            # Check for Chrome crash indicators
            chrome_crash_files = [
                Path("js/Crash Reports"),
                Path("js/chrome_debug.log"),
                Path("js/SingletonLock")
            ]

            for crash_file in chrome_crash_files:
                if crash_file.exists():
                    corruption_indicators.append(f"Chrome crash indicator found: {crash_file}")

            if corruption_indicators:
                logger.warning(f"Session corruption detected: {corruption_indicators}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error detecting session corruption: {e}")
            return True  # Assume corruption if we can't check

    async def recover_from_corruption(self):
        """Recover from session corruption."""
        try:
            logger.info("Attempting to recover from session corruption...")

            # Stop the client if running
            if hasattr(self.whatsapp_client, 'is_running') and self.whatsapp_client.is_running():
                await self.whatsapp_client.stop()
                await asyncio.sleep(3)

            # Clean up all session data
            await self._cleanup_session_files()

            # Wait for cleanup to complete
            await asyncio.sleep(2)

            # Reset authentication state
            self.is_authenticated = False
            self.session_valid = False
            self.authentication_pending = False
            self.qr_code_data = None
            self.last_session_check = None

            logger.info("Session corruption recovery completed")

        except Exception as e:
            logger.error(f"Error recovering from session corruption: {e}")
    
    def get_qr_code_base64(self) -> Optional[str]:
        """Get the current QR code as base64 string."""
        try:
            if not self.qr_code_data:
                return None
            
            # Generate QR code in memory
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(self.qr_code_data)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return img_str
            
        except Exception as e:
            logger.error(f"Error generating QR code base64: {e}")
            return None
