"""
Response Generator for WhatsApp AI Assistant.
Generates contextually appropriate AI responses using RAG and conversation memory.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
import json
import re

from utils.logging import get_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, AIResponse

from .ai_providers.openai_provider import OpenAIProvider
from .context_retrieval import ContextRetrieval
from .memory_manager import MemoryManager

logger = get_logger("response_generator")


class ResponseGenerator:
    """Generates contextually appropriate AI responses for WhatsApp messages."""
    
    def __init__(self, 
                 ai_provider: OpenAIProvider,
                 context_retrieval: ContextRetrieval,
                 memory_manager: MemoryManager):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.ai_provider = ai_provider
        self.context_retrieval = context_retrieval
        self.memory_manager = memory_manager
        
        # Response generation settings
        self.max_response_length = self.config.ai.response.max_length
        self.min_confidence_threshold = self.config.ai.response.min_confidence_threshold
        self.context_window_size = self.config.ai.response.context_window_size
        self.temperature = self.config.ai.response.temperature
        
        # System prompts
        self.base_system_prompt = self._load_base_system_prompt()
        self.personality_traits = self.config.ai.personality
        
        # Response templates
        self.response_templates = {
            'greeting': [
                "Hello! How can I help you today?",
                "Hi there! What's on your mind?",
                "Hey! Good to hear from you!"
            ],
            'farewell': [
                "Take care! Talk to you soon.",
                "Goodbye! Have a great day!",
                "See you later!"
            ],
            'clarification': [
                "Could you tell me more about that?",
                "I'd like to understand better. Can you elaborate?",
                "Can you provide more details?"
            ],
            'acknowledgment': [
                "I understand.",
                "Got it, thanks for letting me know.",
                "That makes sense."
            ]
        }
        
        # State
        self.is_initialized = False
        
        logger.info("Response generator initialized")
    
    async def initialize(self) -> bool:
        """Initialize the response generator."""
        try:
            if not self.ai_provider.is_initialized:
                logger.error("AI provider not initialized")
                return False
            
            if not self.context_retrieval.is_initialized:
                logger.error("Context retrieval not initialized")
                return False
            
            if not self.memory_manager.is_initialized:
                logger.error("Memory manager not initialized")
                return False
            
            self.is_initialized = True
            logger.info("Response generator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize response generator: {e}")
            return False
    
    async def generate_response(self, 
                              contact_id: int,
                              message_text: str,
                              chat_id: str,
                              message_id: Optional[int] = None) -> Dict[str, Any]:
        """Generate an AI response for a WhatsApp message."""
        try:
            start_time = datetime.now()
            
            # Get comprehensive context
            context = await self.context_retrieval.retrieve_context(
                contact_id=contact_id,
                current_message=message_text,
                chat_id=chat_id
            )
            
            # Analyze message intent and sentiment
            message_analysis = await self._analyze_message(message_text)
            
            # Determine response strategy
            response_strategy = await self._determine_response_strategy(
                message_text, message_analysis, context
            )
            
            # Generate response using AI
            ai_response = await self._generate_ai_response(
                message_text, context, message_analysis, response_strategy
            )
            
            if not ai_response.get('success'):
                return ai_response
            
            # Post-process response
            processed_response = await self._post_process_response(
                ai_response['content'], contact_id, message_analysis
            )
            
            # Calculate confidence score
            confidence_score = await self._calculate_confidence_score(
                processed_response, context, message_analysis
            )
            
            # Store AI response in database
            stored_response = await self._store_ai_response(
                contact_id=contact_id,
                original_message=message_text,
                generated_response=processed_response,
                confidence_score=confidence_score,
                context_data=context,
                message_id=message_id,
                response_strategy=response_strategy
            )
            
            end_time = datetime.now()
            generation_time = (end_time - start_time).total_seconds()
            
            result = {
                'success': True,
                'response_text': processed_response,
                'confidence_score': confidence_score,
                'response_strategy': response_strategy,
                'message_analysis': message_analysis,
                'context_score': context.get('context_score', 0.0),
                'generation_time': generation_time,
                'ai_response_id': stored_response.id if stored_response else None,
                'should_send': confidence_score >= self.min_confidence_threshold,
                'metadata': {
                    'model_used': ai_response.get('model'),
                    'tokens_used': ai_response.get('usage', {}).get('total_tokens', 0),
                    'timestamp': end_time.isoformat()
                }
            }
            
            logger.info(f"Generated response for contact {contact_id} with confidence {confidence_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _analyze_message(self, message_text: str) -> Dict[str, Any]:
        """Analyze message intent, sentiment, and characteristics."""
        try:
            analysis = {
                'intent': 'unknown',
                'sentiment': 'neutral',
                'urgency': 'normal',
                'question_type': None,
                'entities': [],
                'topics': [],
                'requires_response': True
            }
            
            text_lower = message_text.lower().strip()
            
            # Intent detection (simple rule-based)
            if any(greeting in text_lower for greeting in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
                analysis['intent'] = 'greeting'
            elif any(farewell in text_lower for farewell in ['bye', 'goodbye', 'see you', 'talk later']):
                analysis['intent'] = 'farewell'
            elif '?' in message_text:
                analysis['intent'] = 'question'
                analysis['question_type'] = self._classify_question(message_text)
            elif any(thanks in text_lower for thanks in ['thank', 'thanks', 'appreciate']):
                analysis['intent'] = 'gratitude'
            elif any(urgent in text_lower for urgent in ['urgent', 'asap', 'emergency', 'help']):
                analysis['urgency'] = 'high'
            
            # Sentiment analysis (simple keyword-based)
            positive_words = ['good', 'great', 'happy', 'love', 'like', 'amazing', 'wonderful', 'excellent']
            negative_words = ['bad', 'sad', 'angry', 'hate', 'terrible', 'awful', 'disappointed', 'frustrated']
            
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                analysis['sentiment'] = 'positive'
            elif negative_count > positive_count:
                analysis['sentiment'] = 'negative'
            
            # Topic extraction (simple keyword matching)
            topic_keywords = {
                'work': ['work', 'job', 'office', 'meeting', 'project', 'boss'],
                'family': ['family', 'wife', 'husband', 'son', 'daughter', 'parent'],
                'health': ['doctor', 'hospital', 'sick', 'medicine', 'health'],
                'travel': ['travel', 'trip', 'vacation', 'flight', 'hotel'],
                'food': ['food', 'restaurant', 'dinner', 'lunch', 'cooking'],
                'technology': ['computer', 'phone', 'app', 'software', 'internet']
            }
            
            for topic, keywords in topic_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    analysis['topics'].append(topic)
            
            # Check if response is required
            if analysis['intent'] in ['greeting', 'question'] or analysis['urgency'] == 'high':
                analysis['requires_response'] = True
            elif analysis['intent'] in ['farewell', 'gratitude']:
                analysis['requires_response'] = False  # Optional response
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing message: {e}")
            return {'intent': 'unknown', 'sentiment': 'neutral', 'requires_response': True}
    
    def _classify_question(self, message_text: str) -> str:
        """Classify the type of question being asked."""
        text_lower = message_text.lower()
        
        if any(word in text_lower for word in ['what', 'which']):
            return 'what'
        elif any(word in text_lower for word in ['when', 'time']):
            return 'when'
        elif any(word in text_lower for word in ['where', 'location']):
            return 'where'
        elif any(word in text_lower for word in ['why', 'reason']):
            return 'why'
        elif any(word in text_lower for word in ['how', 'way']):
            return 'how'
        elif any(word in text_lower for word in ['who', 'person']):
            return 'who'
        elif any(word in text_lower for word in ['can', 'could', 'would', 'will']):
            return 'capability'
        else:
            return 'general'
    
    async def _determine_response_strategy(self, 
                                         message_text: str,
                                         message_analysis: Dict[str, Any],
                                         context: Dict[str, Any]) -> str:
        """Determine the best strategy for responding to the message."""
        try:
            intent = message_analysis.get('intent', 'unknown')
            urgency = message_analysis.get('urgency', 'normal')
            context_score = context.get('context_score', 0.0)
            
            # High urgency messages
            if urgency == 'high':
                return 'urgent_response'
            
            # Intent-based strategies
            if intent == 'greeting':
                return 'friendly_greeting'
            elif intent == 'farewell':
                return 'polite_farewell'
            elif intent == 'question':
                question_type = message_analysis.get('question_type', 'general')
                if question_type in ['what', 'how', 'why']:
                    return 'informative_response'
                else:
                    return 'helpful_response'
            elif intent == 'gratitude':
                return 'acknowledgment'
            
            # Context-based strategies
            if context_score > 0.8:
                return 'contextual_response'
            elif context_score > 0.5:
                return 'semi_contextual_response'
            else:
                return 'general_response'
                
        except Exception as e:
            logger.error(f"Error determining response strategy: {e}")
            return 'general_response'
    
    async def _generate_ai_response(self, 
                                  message_text: str,
                                  context: Dict[str, Any],
                                  message_analysis: Dict[str, Any],
                                  response_strategy: str) -> Dict[str, Any]:
        """Generate AI response using the language model."""
        try:
            # Build system prompt
            system_prompt = self._build_system_prompt(response_strategy, message_analysis)
            
            # Build conversation messages
            messages = self._build_conversation_messages(message_text, context)
            
            # Get formatted context
            formatted_context = context.get('formatted_context', '')
            
            # Generate response
            response = await self.ai_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                context=formatted_context,
                temperature=self.temperature,
                max_tokens=self.max_response_length
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return {'success': False, 'error': str(e)}
    
    def _build_system_prompt(self, response_strategy: str, message_analysis: Dict[str, Any]) -> str:
        """Build system prompt based on response strategy and message analysis."""
        base_prompt = self.base_system_prompt
        
        # Add personality traits
        if hasattr(self.personality_traits, 'model_dump'):
            # Pydantic v2 style
            personality_dict = self.personality_traits.model_dump()
        elif hasattr(self.personality_traits, 'dict'):
            # Pydantic v1 style
            personality_dict = self.personality_traits.dict()
        else:
            # Fallback to __dict__
            personality_dict = self.personality_traits.__dict__

        personality_text = ", ".join([f"{trait}: {value}" for trait, value in personality_dict.items()])
        base_prompt += f"\n\nPersonality: {personality_text}"
        
        # Add strategy-specific instructions
        strategy_instructions = {
            'urgent_response': "This is an urgent message. Respond promptly and offer immediate help.",
            'friendly_greeting': "Respond with a warm, friendly greeting. Match the energy level.",
            'polite_farewell': "Respond with a polite farewell. Keep it brief and warm.",
            'informative_response': "Provide helpful, accurate information. Be thorough but concise.",
            'helpful_response': "Be helpful and supportive. Offer assistance where appropriate.",
            'acknowledgment': "Acknowledge their gratitude warmly and briefly.",
            'contextual_response': "Use the provided context to give a personalized, relevant response.",
            'semi_contextual_response': "Use available context but don't assume too much.",
            'general_response': "Provide a helpful, general response. Be friendly and supportive."
        }
        
        if response_strategy in strategy_instructions:
            base_prompt += f"\n\nResponse Strategy: {strategy_instructions[response_strategy]}"
        
        # Add message analysis context
        intent = message_analysis.get('intent', 'unknown')
        sentiment = message_analysis.get('sentiment', 'neutral')
        
        base_prompt += f"\n\nMessage Intent: {intent}"
        base_prompt += f"\nMessage Sentiment: {sentiment}"
        
        # Add response guidelines
        base_prompt += (
            "\n\nGuidelines:"
            "\n- Keep responses natural and conversational"
            "\n- Match the tone and formality level of the sender"
            "\n- Be helpful and supportive"
            "\n- Don't make assumptions about information not provided"
            "\n- If you're unsure, ask for clarification"
            "\n- Keep responses concise but complete"
        )
        
        return base_prompt
    
    def _build_conversation_messages(self, current_message: str, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """Build conversation messages for the AI model."""
        messages = []
        
        # Add recent conversation history
        recent_messages = context.get('components', {}).get('recent_messages', {}).get('messages', [])
        
        # Limit to last few messages to avoid token limits
        for msg in recent_messages[-5:]:
            role = "assistant" if msg['is_from_me'] else "user"
            messages.append({
                "role": role,
                "content": msg['text']
            })
        
        # Add current message
        messages.append({
            "role": "user",
            "content": current_message
        })
        
        return messages
    
    async def _post_process_response(self, 
                                   response_text: str,
                                   contact_id: int,
                                   message_analysis: Dict[str, Any]) -> str:
        """Post-process the AI response for quality and appropriateness."""
        try:
            processed_text = response_text.strip()
            
            # Remove any unwanted prefixes/suffixes
            prefixes_to_remove = ["AI:", "Assistant:", "Bot:", "Response:"]
            for prefix in prefixes_to_remove:
                if processed_text.startswith(prefix):
                    processed_text = processed_text[len(prefix):].strip()
            
            # Ensure appropriate length
            if len(processed_text) > self.max_response_length:
                # Truncate at sentence boundary if possible
                sentences = processed_text.split('. ')
                truncated = ""
                for sentence in sentences:
                    if len(truncated + sentence + '. ') <= self.max_response_length:
                        truncated += sentence + '. '
                    else:
                        break
                processed_text = truncated.strip()
            
            # Ensure minimum quality
            if len(processed_text) < 10:
                # Use template response if generated response is too short
                intent = message_analysis.get('intent', 'unknown')
                if intent in self.response_templates:
                    processed_text = self.response_templates[intent][0]
                else:
                    processed_text = "I understand. How can I help you with that?"
            
            # Basic content filtering
            processed_text = await self._filter_inappropriate_content(processed_text)
            
            return processed_text
            
        except Exception as e:
            logger.error(f"Error post-processing response: {e}")
            return response_text
    
    async def _filter_inappropriate_content(self, text: str) -> str:
        """Filter inappropriate content from response."""
        try:
            # Use OpenAI moderation API if available
            moderation_result = await self.ai_provider.moderate_content(text)
            
            if moderation_result.get('success') and moderation_result.get('flagged'):
                logger.warning("Generated response flagged by moderation API")
                return "I apologize, but I can't provide that type of response. How else can I help you?"
            
            return text
            
        except Exception as e:
            logger.error(f"Error filtering content: {e}")
            return text
    
    async def _calculate_confidence_score(self, 
                                        response_text: str,
                                        context: Dict[str, Any],
                                        message_analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for the generated response."""
        try:
            confidence_factors = []
            
            # Context quality factor
            context_score = context.get('context_score', 0.0)
            confidence_factors.append(context_score * 0.3)
            
            # Response length factor (not too short, not too long)
            length_score = min(1.0, len(response_text) / 100.0)
            if len(response_text) > 500:
                length_score *= 0.8  # Penalize very long responses
            confidence_factors.append(length_score * 0.2)
            
            # Intent matching factor
            intent = message_analysis.get('intent', 'unknown')
            if intent != 'unknown':
                confidence_factors.append(0.8 * 0.2)
            else:
                confidence_factors.append(0.4 * 0.2)
            
            # Sentiment appropriateness factor
            sentiment = message_analysis.get('sentiment', 'neutral')
            if sentiment in ['positive', 'neutral']:
                confidence_factors.append(0.8 * 0.1)
            else:
                confidence_factors.append(0.6 * 0.1)  # Lower confidence for negative sentiment
            
            # Response quality factor (basic heuristics)
            quality_score = 0.7  # Base quality score
            if '?' in response_text and message_analysis.get('intent') != 'question':
                quality_score += 0.1  # Good to ask clarifying questions
            if len(response_text.split()) > 5:
                quality_score += 0.1  # Substantial response
            
            confidence_factors.append(quality_score * 0.2)
            
            # Calculate final confidence score
            final_confidence = sum(confidence_factors)
            final_confidence = max(0.0, min(1.0, final_confidence))  # Clamp to [0, 1]
            
            return final_confidence
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.5  # Default moderate confidence
    
    async def _store_ai_response(self, 
                               contact_id: int,
                               original_message: str,
                               generated_response: str,
                               confidence_score: float,
                               context_data: Dict[str, Any],
                               message_id: Optional[int],
                               response_strategy: str) -> Optional[AIResponse]:
        """Store AI response in database."""
        try:
            with self.db_manager.get_session() as session:
                ai_response = AIResponse(
                    contact_id=contact_id,
                    original_message_id=message_id,
                    original_text=original_message,
                    generated_text=generated_response,
                    confidence_score=confidence_score,
                    response_strategy=response_strategy,
                    context_data=context_data,
                    model_used=self.ai_provider.model,
                    status='generated',
                    created_at=datetime.now()
                )
                
                session.add(ai_response)
                session.commit()
                session.refresh(ai_response)
                
                logger.debug(f"Stored AI response {ai_response.id}")
                return ai_response
                
        except Exception as e:
            logger.error(f"Error storing AI response: {e}")
            return None
    
    def _load_base_system_prompt(self) -> str:
        """Load the base system prompt for the AI assistant."""
        return """You are a helpful WhatsApp AI assistant. You help users by responding to their messages in a natural, conversational way.

Key characteristics:
- You are friendly, helpful, and supportive
- You respond naturally as if you're a close friend or assistant
- You remember context from previous conversations
- You ask clarifying questions when needed
- You provide helpful information and assistance
- You maintain appropriate boundaries and privacy
- You are concise but thorough in your responses

Your goal is to be genuinely helpful while maintaining a warm, personal touch in all interactions."""
    
    def get_status(self) -> Dict[str, Any]:
        """Get response generator status."""
        return {
            'initialized': self.is_initialized,
            'max_response_length': self.max_response_length,
            'min_confidence_threshold': self.min_confidence_threshold,
            'temperature': self.temperature,
            'ai_provider_status': self.ai_provider.get_status() if self.ai_provider else None
        }
